---
name: Bug Report
about: Create a report to help us improve
title: '[BUG] '
labels: bug
assignees: ''
---

## Bug Description
A clear and concise description of what the bug is.

## To Reproduce
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## Expected Behavior
A clear and concise description of what you expected to happen.

## Actual Behavior
A clear and concise description of what actually happened.

## Screenshots
If applicable, add screenshots to help explain your problem.

## Environment
- OS: [e.g. Ubuntu 22.04]
- Rust Version: [e.g. 1.75.0]
- Infinitium Signal Version: [e.g. 0.1.0]
- Database: [e.g. PostgreSQL 15]

## Additional Context
Add any other context about the problem here.

## Security Impact
If this bug has security implications, please describe them here.

## Logs
Please include relevant log output:
```
[paste logs here]
```

## Configuration
Please include relevant configuration (remove sensitive data):
```yaml
[paste config here]
```

---

## Feature Request Template

---
name: Feature Request
about: Suggest an idea for this project
title: '[FEATURE] '
labels: enhancement
assignees: ''
---

## Is your feature request related to a problem?
A clear and concise description of what the problem is. Ex. I'm always frustrated when [...]

## Describe the solution you'd like
A clear and concise description of what you want to happen.

## Describe alternatives you've considered
A clear and concise description of any alternative solutions or features you've considered.

## Additional context
Add any other context or screenshots about the feature request here.

## Implementation Details
If you have ideas about how this could be implemented, please describe them here.

## Security Considerations
Please describe any security implications of this feature.

---

## Security Vulnerability Template

---
name: Security Vulnerability
about: Report a security vulnerability (use private disclosure)
title: '[SECURITY] '
labels: security
assignees: ''
---

**⚠️ IMPORTANT: Do not report security vulnerabilities in public issues.**

Please report security vulnerabilities privately by:
1. Emailing <EMAIL>
2. Using GitHub's private vulnerability reporting feature
3. Following our security policy in SECURITY.md

## Vulnerability Type
- [ ] Authentication bypass
- [ ] Authorization bypass
- [ ] SQL injection
- [ ] Cross-site scripting (XSS)
- [ ] Cross-site request forgery (CSRF)
- [ ] Remote code execution
- [ ] Information disclosure
- [ ] Denial of service
- [ ] Other: ___________

## Affected Components
- [ ] API endpoints
- [ ] Database layer
- [ ] Authentication system
- [ ] Blockchain integration
- [ ] SBOM/HBOM scanners
- [ ] Compliance exporters
- [ ] Other: ___________

## Severity Assessment
- [ ] Critical
- [ ] High
- [ ] Medium
- [ ] Low

## Description
[Provide a detailed description of the vulnerability]

## Steps to Reproduce
[Provide step-by-step instructions]

## Impact
[Describe the potential impact of this vulnerability]

## Mitigation
[If you have suggestions for fixing this vulnerability, include them here]
