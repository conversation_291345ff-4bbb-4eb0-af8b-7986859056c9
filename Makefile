# Infinitium Signal - Makefile
# Enterprise Cyber-Compliance Platform

.PHONY: help setup build test clean run dev docker deploy docs lint format check

# Default target
.DEFAULT_GOAL := help

# Variables
CARGO := cargo
DOCKER := docker
KUBECTL := kubectl
HELM := helm
PROJECT_NAME := infinitum-signal
VERSION := $(shell grep '^version' Cargo.toml | sed 's/.*"\(.*\)".*/\1/')
DOCKER_IMAGE := $(PROJECT_NAME):$(VERSION)
DOCKER_REGISTRY := ghcr.io/infinitum-signal

# Colors for output
RED := \033[31m
GREEN := \033[32m
YELLOW := \033[33m
BLUE := \033[34m
RESET := \033[0m

help: ## Show this help message
	@echo "$(BLUE)Infinitium Signal - Enterprise Cyber-Compliance Platform$(RESET)"
	@echo ""
	@echo "$(GREEN)Available targets:$(RESET)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-20s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# =============================================================================
# SETUP & INSTALLATION
# =============================================================================

setup: ## Setup development environment
	@echo "$(GREEN)Setting up development environment...$(RESET)"
	@rustup update
	@rustup component add clippy rustfmt
	@cargo install cargo-watch cargo-tarpaulin diesel_cli sqlx-cli
	@cp .env.example .env
	@echo "$(GREEN)✓ Development environment setup complete$(RESET)"

install-deps: ## Install system dependencies
	@echo "$(GREEN)Installing system dependencies...$(RESET)"
	@if command -v apt-get >/dev/null 2>&1; then \
		sudo apt-get update && \
		sudo apt-get install -y postgresql-client redis-tools wkhtmltopdf; \
	elif command -v brew >/dev/null 2>&1; then \
		brew install postgresql redis wkhtmltopdf; \
	else \
		echo "$(RED)Please install PostgreSQL, Redis, and wkhtmltopdf manually$(RESET)"; \
	fi

# =============================================================================
# BUILD & COMPILATION
# =============================================================================

build: ## Build the project in release mode
	@echo "$(GREEN)Building project...$(RESET)"
	@$(CARGO) build --release
	@echo "$(GREEN)✓ Build complete$(RESET)"

build-dev: ## Build the project in development mode
	@echo "$(GREEN)Building project (dev mode)...$(RESET)"
	@$(CARGO) build
	@echo "$(GREEN)✓ Development build complete$(RESET)"

clean: ## Clean build artifacts
	@echo "$(GREEN)Cleaning build artifacts...$(RESET)"
	@$(CARGO) clean
	@rm -rf target/
	@echo "$(GREEN)✓ Clean complete$(RESET)"

# =============================================================================
# TESTING
# =============================================================================

test: ## Run all tests
	@echo "$(GREEN)Running tests...$(RESET)"
	@$(CARGO) test --all-features
	@echo "$(GREEN)✓ All tests passed$(RESET)"

test-unit: ## Run unit tests only
	@echo "$(GREEN)Running unit tests...$(RESET)"
	@$(CARGO) test --lib --bins
	@echo "$(GREEN)✓ Unit tests passed$(RESET)"

test-integration: ## Run integration tests only
	@echo "$(GREEN)Running integration tests...$(RESET)"
	@$(CARGO) test --test '*'
	@echo "$(GREEN)✓ Integration tests passed$(RESET)"

test-coverage: ## Run tests with coverage report
	@echo "$(GREEN)Running tests with coverage...$(RESET)"
	@$(CARGO) tarpaulin --out Html --output-dir coverage/
	@echo "$(GREEN)✓ Coverage report generated in coverage/$(RESET)"

bench: ## Run benchmarks
	@echo "$(GREEN)Running benchmarks...$(RESET)"
	@$(CARGO) bench
	@echo "$(GREEN)✓ Benchmarks complete$(RESET)"

# =============================================================================
# CODE QUALITY
# =============================================================================

lint: ## Run clippy linter
	@echo "$(GREEN)Running clippy...$(RESET)"
	@$(CARGO) clippy --all-targets --all-features -- -D warnings
	@echo "$(GREEN)✓ Linting complete$(RESET)"

format: ## Format code with rustfmt
	@echo "$(GREEN)Formatting code...$(RESET)"
	@$(CARGO) fmt
	@echo "$(GREEN)✓ Code formatted$(RESET)"

format-check: ## Check code formatting
	@echo "$(GREEN)Checking code formatting...$(RESET)"
	@$(CARGO) fmt -- --check
	@echo "$(GREEN)✓ Code formatting is correct$(RESET)"

check: ## Run all checks (lint, format, test)
	@echo "$(GREEN)Running all checks...$(RESET)"
	@$(MAKE) format-check
	@$(MAKE) lint
	@$(MAKE) test
	@echo "$(GREEN)✓ All checks passed$(RESET)"

# =============================================================================
# DEVELOPMENT
# =============================================================================

run: ## Run the application
	@echo "$(GREEN)Starting Infinitium Signal...$(RESET)"
	@$(CARGO) run --release

dev: ## Run in development mode with auto-reload
	@echo "$(GREEN)Starting development server with auto-reload...$(RESET)"
	@$(CARGO) watch -x 'run'

dev-api: ## Run API server only
	@echo "$(GREEN)Starting API server...$(RESET)"
	@$(CARGO) run --bin infinitum-signal

dev-cli: ## Run CLI tool
	@echo "$(GREEN)Starting CLI tool...$(RESET)"
	@$(CARGO) run --bin infinitum-signal-cli

# =============================================================================
# DATABASE
# =============================================================================

db-setup: ## Setup database
	@echo "$(GREEN)Setting up database...$(RESET)"
	@createdb infinitum_signal || true
	@$(MAKE) migrate
	@echo "$(GREEN)✓ Database setup complete$(RESET)"

migrate: ## Run database migrations
	@echo "$(GREEN)Running database migrations...$(RESET)"
	@sqlx migrate run
	@echo "$(GREEN)✓ Migrations complete$(RESET)"

migrate-revert: ## Revert last migration
	@echo "$(YELLOW)Reverting last migration...$(RESET)"
	@sqlx migrate revert
	@echo "$(GREEN)✓ Migration reverted$(RESET)"

db-reset: ## Reset database (WARNING: destroys all data)
	@echo "$(RED)WARNING: This will destroy all data!$(RESET)"
	@read -p "Are you sure? [y/N] " -n 1 -r; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		dropdb infinitum_signal || true; \
		createdb infinitum_signal; \
		$(MAKE) migrate; \
		echo "$(GREEN)✓ Database reset complete$(RESET)"; \
	else \
		echo "$(YELLOW)Database reset cancelled$(RESET)"; \
	fi

# =============================================================================
# DOCKER
# =============================================================================

docker-build: ## Build Docker image
	@echo "$(GREEN)Building Docker image...$(RESET)"
	@$(DOCKER) build -t $(DOCKER_IMAGE) .
	@echo "$(GREEN)✓ Docker image built: $(DOCKER_IMAGE)$(RESET)"

docker-run: ## Run Docker container
	@echo "$(GREEN)Running Docker container...$(RESET)"
	@$(DOCKER) run -p 8080:8080 --env-file .env $(DOCKER_IMAGE)

docker-compose-up: ## Start all services with Docker Compose
	@echo "$(GREEN)Starting services with Docker Compose...$(RESET)"
	@docker-compose up -d
	@echo "$(GREEN)✓ Services started$(RESET)"

docker-compose-down: ## Stop all services
	@echo "$(GREEN)Stopping services...$(RESET)"
	@docker-compose down
	@echo "$(GREEN)✓ Services stopped$(RESET)"

docker-compose-logs: ## View Docker Compose logs
	@docker-compose logs -f

# =============================================================================
# DEPLOYMENT
# =============================================================================

deploy-k8s: ## Deploy to Kubernetes
	@echo "$(GREEN)Deploying to Kubernetes...$(RESET)"
	@$(KUBECTL) apply -f deployment/kubernetes/
	@echo "$(GREEN)✓ Deployed to Kubernetes$(RESET)"

deploy-helm: ## Deploy with Helm
	@echo "$(GREEN)Deploying with Helm...$(RESET)"
	@$(HELM) upgrade --install $(PROJECT_NAME) deployment/helm/$(PROJECT_NAME)
	@echo "$(GREEN)✓ Deployed with Helm$(RESET)"

undeploy-k8s: ## Remove from Kubernetes
	@echo "$(GREEN)Removing from Kubernetes...$(RESET)"
	@$(KUBECTL) delete -f deployment/kubernetes/
	@echo "$(GREEN)✓ Removed from Kubernetes$(RESET)"

# =============================================================================
# DOCUMENTATION
# =============================================================================

docs: ## Generate documentation
	@echo "$(GREEN)Generating documentation...$(RESET)"
	@$(CARGO) doc --no-deps --open
	@echo "$(GREEN)✓ Documentation generated$(RESET)"

docs-api: ## Generate API documentation
	@echo "$(GREEN)Generating API documentation...$(RESET)"
	@cargo run --bin generate-api-docs
	@echo "$(GREEN)✓ API documentation generated$(RESET)"

# =============================================================================
# UTILITIES
# =============================================================================

scan-demo: ## Run demo scan
	@echo "$(GREEN)Running demo scan...$(RESET)"
	@./scripts/demo.sh
	@echo "$(GREEN)✓ Demo scan complete$(RESET)"

load-test: ## Run load tests
	@echo "$(GREEN)Running load tests...$(RESET)"
	@./scripts/load_test.sh
	@echo "$(GREEN)✓ Load tests complete$(RESET)"

security-scan: ## Run security scan
	@echo "$(GREEN)Running security scan...$(RESET)"
	@cargo audit
	@echo "$(GREEN)✓ Security scan complete$(RESET)"

backup: ## Backup database
	@echo "$(GREEN)Creating database backup...$(RESET)"
	@./scripts/backup.sh
	@echo "$(GREEN)✓ Backup complete$(RESET)"

version: ## Show version information
	@echo "$(BLUE)Infinitium Signal v$(VERSION)$(RESET)"
	@echo "Rust version: $$(rustc --version)"
	@echo "Cargo version: $$(cargo --version)"

# =============================================================================
# CI/CD HELPERS
# =============================================================================

ci-setup: ## Setup CI environment
	@echo "$(GREEN)Setting up CI environment...$(RESET)"
	@rustup component add clippy rustfmt
	@cargo install cargo-tarpaulin

ci-test: ## Run CI tests
	@echo "$(GREEN)Running CI tests...$(RESET)"
	@$(MAKE) format-check
	@$(MAKE) lint
	@$(MAKE) test
	@$(MAKE) security-scan

ci-build: ## Build for CI
	@echo "$(GREEN)Building for CI...$(RESET)"
	@$(CARGO) build --release --locked

ci-publish: ## Publish Docker image
	@echo "$(GREEN)Publishing Docker image...$(RESET)"
	@$(DOCKER) tag $(DOCKER_IMAGE) $(DOCKER_REGISTRY)/$(DOCKER_IMAGE)
	@$(DOCKER) push $(DOCKER_REGISTRY)/$(DOCKER_IMAGE)
