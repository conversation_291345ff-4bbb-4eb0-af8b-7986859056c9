apiVersion: apps/v1
kind: Deployment
metadata:
  name: infinitum-signal
  namespace: infinitum-signal
  labels:
    app: infinitum-signal
    component: api-server
    version: v1.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: infinitum-signal
      component: api-server
  template:
    metadata:
      labels:
        app: infinitum-signal
        component: api-server
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: infinitum-signal
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
      containers:
      - name: infinitum-signal
        image: infinitum-signal:latest
        imagePullPolicy: IfNotPresent
        ports:
        - name: http
          containerPort: 8080
          protocol: TCP
        - name: metrics
          containerPort: 9090
          protocol: TCP
        env:
        - name: APP_ENV
          value: "production"
        - name: RUST_LOG
          value: "info"
        - name: API_HOST
          value: "0.0.0.0"
        - name: API_PORT
          value: "8080"
        - name: METRICS_PORT
          value: "9090"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: infinitum-signal-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: infinitum-signal-secrets
              key: redis-url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: infinitum-signal-secrets
              key: jwt-secret
        - name: ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: infinitum-signal-secrets
              key: encryption-key
        - name: SNYK_TOKEN
          valueFrom:
            secretKeyRef:
              name: infinitum-signal-secrets
              key: snyk-token
              optional: true
        - name: NVD_API_KEY
          valueFrom:
            secretKeyRef:
              name: infinitum-signal-secrets
              key: nvd-api-key
              optional: true
        volumeMounts:
        - name: config
          mountPath: /app/config
          readOnly: true
        - name: data
          mountPath: /app/data
        - name: logs
          mountPath: /app/logs
        - name: output
          mountPath: /app/output
        - name: tmp
          mountPath: /tmp
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: config
        configMap:
          name: infinitum-signal-config
      - name: data
        persistentVolumeClaim:
          claimName: infinitum-signal-data
      - name: logs
        persistentVolumeClaim:
          claimName: infinitum-signal-logs
      - name: output
        persistentVolumeClaim:
          claimName: infinitum-signal-output
      - name: tmp
        emptyDir: {}
      nodeSelector:
        kubernetes.io/os: linux
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - infinitum-signal
              topologyKey: kubernetes.io/hostname

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: infinitum-signal-worker
  namespace: infinitum-signal
  labels:
    app: infinitum-signal
    component: worker
    version: v1.0.0
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: infinitum-signal
      component: worker
  template:
    metadata:
      labels:
        app: infinitum-signal
        component: worker
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: infinitum-signal
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        runAsGroup: 1001
        fsGroup: 1001
      containers:
      - name: infinitum-signal-worker
        image: infinitum-signal:latest
        imagePullPolicy: IfNotPresent
        command: ["/usr/local/bin/docker-entrypoint.sh"]
        args: ["worker"]
        ports:
        - name: metrics
          containerPort: 9090
          protocol: TCP
        env:
        - name: APP_ENV
          value: "production"
        - name: RUST_LOG
          value: "info"
        - name: WORKER_MODE
          value: "true"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: infinitum-signal-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: infinitum-signal-secrets
              key: redis-url
        - name: SNYK_TOKEN
          valueFrom:
            secretKeyRef:
              name: infinitum-signal-secrets
              key: snyk-token
              optional: true
        - name: NVD_API_KEY
          valueFrom:
            secretKeyRef:
              name: infinitum-signal-secrets
              key: nvd-api-key
              optional: true
        volumeMounts:
        - name: config
          mountPath: /app/config
          readOnly: true
        - name: data
          mountPath: /app/data
        - name: logs
          mountPath: /app/logs
        - name: output
          mountPath: /app/output
        - name: tmp
          mountPath: /tmp
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: config
        configMap:
          name: infinitum-signal-config
      - name: data
        persistentVolumeClaim:
          claimName: infinitum-signal-data
      - name: logs
        persistentVolumeClaim:
          claimName: infinitum-signal-logs
      - name: output
        persistentVolumeClaim:
          claimName: infinitum-signal-output
      - name: tmp
        emptyDir: {}
      nodeSelector:
        kubernetes.io/os: linux
