apiVersion: v2
name: infinitum-signal
description: Enterprise Cyber-Compliance Platform with SBOM/HBOM scanning, vulnerability analysis, and blockchain audit trails
type: application
version: 0.1.0
appVersion: "0.1.0"

keywords:
  - cybersecurity
  - compliance
  - sbom
  - vulnerability
  - blockchain
  - audit
  - scanning

home: https://infinitum-signal.com
sources:
  - https://github.com/infinitum-signal/infinitum-signal

maintainers:
  - name: Infinitum Signal Team
    email: <EMAIL>
    url: https://infinitum-signal.com

annotations:
  category: Security
  licenses: Apache-2.0
  images: |
    - name: infinitum-signal
      image: ghcr.io/infinitum-signal/infinitum-signal:0.1.0
    - name: postgresql
      image: postgres:14
    - name: redis
      image: redis:7-alpine

dependencies:
  - name: postgresql
    version: "12.1.9"
    repository: https://charts.bitnami.com/bitnami
    condition: postgresql.enabled
  - name: redis
    version: "17.4.3"
    repository: https://charts.bitnami.com/bitnami
    condition: redis.enabled
  - name: prometheus
    version: "15.18.0"
    repository: https://prometheus-community.github.io/helm-charts
    condition: monitoring.prometheus.enabled
  - name: grafana
    version: "6.50.7"
    repository: https://grafana.github.io/helm-charts
    condition: monitoring.grafana.enabled
