# Infinitum Signal Enterprise Cyber-Compliance Platform

[![Build Status](https://github.com/infinitum-signal/infinitum-signal/workflows/CI/badge.svg)](https://github.com/infinitum-signal/infinitum-signal/actions)
[![Security Scan](https://github.com/infinitum-signal/infinitum-signal/workflows/Security/badge.svg)](https://github.com/infinitum-signal/infinitum-signal/actions)
[![License](https://img.shields.io/badge/license-Apache%202.0-blue.svg)](LICENSE)
[![Rust Version](https://img.shields.io/badge/rust-1.75+-orange.svg)](https://www.rust-lang.org)

## 🚀 Overview

Infinitum Signal is a production-grade, enterprise cybersecurity compliance platform built in Rust. It provides comprehensive SBOM/HBOM scanning, vulnerability analysis, blockchain-based audit trails, and automated compliance reporting for multiple regulatory frameworks.

### 🎯 Key Features

- **🔍 Advanced SBOM/HBOM Scanning**: Multi-language dependency resolution with Trivy, Syft, and Binwalk integration
- **🛡️ Vulnerability Analysis**: Real-time CVE correlation with NVD, GitHub Advisory, and custom feeds
- **⛓️ Blockchain Audit Trails**: Hyperledger Fabric integration with verifiable credentials
- **📋 Compliance Automation**: CERT-In, SEBI, RBI, ISO-27001, SOC-2, GDPR reporting
- **🤖 ML-Powered Insights**: Anomaly detection and predictive risk assessment
- **📊 Real-time Monitoring**: Enterprise dashboards with WebSocket updates
- **🔐 Enterprise Security**: JWT authentication, RBAC, MFA, and zero-trust architecture

### 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web UI        │    │   API Gateway   │    │   Scanners      │
│   Dashboard     │◄──►│   (Axum)        │◄──►│   SBOM/HBOM     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   Database      │    │   Blockchain    │
                       │   (PostgreSQL)  │    │   (Hyperledger) │
                       └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- Rust 1.75+ with Cargo
- PostgreSQL 14+
- Redis 6+
- Docker & Docker Compose
- Kubernetes (optional, for production)

### Installation

```bash
# Clone the repository
git clone https://github.com/infinitum-signal/infinitum-signal.git
cd infinitum-signal

# Install dependencies
cargo build --release

# Set up environment
cp .env.example .env
# Edit .env with your configuration

# Run database migrations
cargo run --bin migrate

# Start the server
cargo run --release
```

### Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up -d

# Or use the production Docker image
docker run -p 8080:8080 infinitum-signal/infinitum-signal:latest
```

### Kubernetes Deployment

```bash
# Deploy using Helm
helm install infinitum-signal ./deployment/helm/infinitum-signal

# Or apply Kubernetes manifests directly
kubectl apply -f deployment/kubernetes/
```

## 📖 Documentation

- [📚 Architecture Guide](docs/ARCHITECTURE.md)
- [🔌 API Documentation](docs/API.md)
- [🛡️ Security Guide](docs/SECURITY.md)
- [📋 Compliance Guide](docs/COMPLIANCE.md)
- [🎬 Demo Script](docs/DEMO_SCRIPT.md)

## 🔧 Configuration

The platform supports configuration through environment variables, YAML files, and command-line arguments:

```yaml
# config.yaml
server:
  host: "0.0.0.0"
  port: 8080
  
database:
  url: "postgresql://user:pass@localhost/infinitum_signal"
  
blockchain:
  network: "hyperledger-fabric"
  channel: "compliance-channel"
  
compliance:
  frameworks: ["cert-in", "sebi", "rbi", "iso-27001"]
```

## 🧪 Testing

```bash
# Run all tests
cargo test

# Run with coverage
cargo test --all-features
cargo tarpaulin --out Html

# Run benchmarks
cargo bench

# Integration tests
cargo test --test integration

# Load testing
./scripts/load_test.sh
```

## 🚀 API Usage

### Scan Project for SBOM

```bash
curl -X POST http://localhost:8080/api/v1/scan \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "project_path": "/path/to/project",
    "scan_type": "sbom",
    "output_format": "cyclonedx"
  }'
```

### Generate Compliance Report

```bash
curl -X POST http://localhost:8080/api/v1/compliance/report \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "framework": "cert-in",
    "project_id": "uuid",
    "format": "pdf"
  }'
```

## 🔐 Security

- **Authentication**: JWT with refresh tokens
- **Authorization**: Role-based access control (RBAC)
- **Encryption**: AES-256 at rest, TLS 1.3 in transit
- **Audit**: Comprehensive logging with blockchain verification
- **Compliance**: SOC-2, ISO-27001, GDPR ready

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/infinitum-signal)
- 📖 Documentation: [docs.infinitum-signal.com](https://docs.infinitum-signal.com)
- 🐛 Issues: [GitHub Issues](https://github.com/infinitum-signal/infinitum-signal/issues)

## 🙏 Acknowledgments

- [Rust Community](https://www.rust-lang.org/community)
- [Tokio Project](https://tokio.rs/)
- [Hyperledger Foundation](https://www.hyperledger.org/)
- [OWASP Foundation](https://owasp.org/)

---

**Built with ❤️ by the Infinitum Signal Team**
