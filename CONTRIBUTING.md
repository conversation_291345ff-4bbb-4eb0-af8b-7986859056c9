# Contributing to Infinitum Signal

Thank you for your interest in contributing to Infinitum Signal! This document provides guidelines and information for contributors.

## 🤝 Code of Conduct

This project adheres to our [Code of Conduct](CODE_OF_CONDUCT.md). By participating, you are expected to uphold this code.

## 🚀 Getting Started

### Prerequisites

- Rust 1.75+ with Cargo
- PostgreSQL 14+
- Redis 6+
- Docker & Docker Compose
- Git

### Development Setup

1. **Fork and Clone**
   ```bash
   git clone https://github.com/YOUR_USERNAME/infinitum-signal.git
   cd infinitum-signal
   ```

2. **Install Dependencies**
   ```bash
   cargo build
   ```

3. **Set Up Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your local configuration
   ```

4. **Run Tests**
   ```bash
   cargo test
   ```

5. **Start Development Server**
   ```bash
   cargo run
   ```

## 📋 How to Contribute

### Reporting Issues

- Use the [GitHub Issues](https://github.com/infinitum-signal/infinitum-signal/issues) page
- Search existing issues before creating a new one
- Use the appropriate issue template
- Provide detailed information including:
  - Steps to reproduce
  - Expected vs actual behavior
  - Environment details
  - Relevant logs or screenshots

### Suggesting Features

- Open a [Feature Request](https://github.com/infinitum-signal/infinitum-signal/issues/new?template=feature_request.md)
- Describe the problem you're trying to solve
- Explain your proposed solution
- Consider the impact on existing users

### Pull Requests

1. **Create a Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes**
   - Follow our coding standards
   - Add tests for new functionality
   - Update documentation as needed
   - Ensure all tests pass

3. **Commit Changes**
   ```bash
   git commit -m "feat: add new SBOM scanner integration"
   ```
   
   Follow [Conventional Commits](https://conventionalcommits.org/) format:
   - `feat:` new features
   - `fix:` bug fixes
   - `docs:` documentation changes
   - `test:` adding tests
   - `refactor:` code refactoring
   - `perf:` performance improvements
   - `chore:` maintenance tasks

4. **Push and Create PR**
   ```bash
   git push origin feature/your-feature-name
   ```
   
   Create a pull request with:
   - Clear title and description
   - Link to related issues
   - Screenshots/demos if applicable

## 🎯 Development Guidelines

### Code Style

- Use `rustfmt` for formatting: `cargo fmt`
- Use `clippy` for linting: `cargo clippy`
- Follow Rust naming conventions
- Write clear, self-documenting code
- Add rustdoc comments for public APIs

### Testing

- Write unit tests for all new functions
- Add integration tests for new features
- Ensure test coverage remains above 85%
- Use property-based testing where appropriate

```bash
# Run all tests
cargo test

# Run with coverage
cargo tarpaulin --out Html

# Run specific test
cargo test test_name
```

### Documentation

- Update README.md for user-facing changes
- Add rustdoc comments for public APIs
- Update API documentation in `docs/API.md`
- Include examples in documentation

### Security

- Follow secure coding practices
- Never commit secrets or credentials
- Use `cargo audit` to check for vulnerabilities
- Report security issues <NAME_EMAIL>

## 🏗️ Architecture Guidelines

### Module Organization

```
src/
├── scanners/     # SBOM/HBOM scanning logic
├── compliance/   # Regulatory framework implementations
├── vulnerability/# CVE analysis and risk assessment
├── blockchain/   # Audit trail and verification
├── api/         # REST API endpoints
├── database/    # Data models and migrations
└── utils/       # Shared utilities
```

### Error Handling

- Use `Result<T, E>` for fallible operations
- Create custom error types with `thiserror`
- Provide meaningful error messages
- Log errors appropriately

### Async Programming

- Use `tokio` for async runtime
- Prefer `async/await` over manual futures
- Use `Arc<Mutex<T>>` sparingly
- Consider using channels for communication

## 🔍 Review Process

### What We Look For

- **Correctness**: Does the code work as intended?
- **Security**: Are there any security vulnerabilities?
- **Performance**: Is the code efficient?
- **Maintainability**: Is the code easy to understand and modify?
- **Testing**: Are there adequate tests?
- **Documentation**: Is the code well-documented?

### Review Timeline

- Initial review within 2-3 business days
- Follow-up reviews within 1-2 business days
- Urgent fixes reviewed within 24 hours

## 🎉 Recognition

Contributors are recognized in:
- Release notes
- Contributors section in README
- Annual contributor appreciation posts

## 📞 Getting Help

- **Discord**: [Join our community](https://discord.gg/infinitum-signal)
- **Email**: <EMAIL>
- **GitHub Discussions**: For general questions
- **GitHub Issues**: For bug reports and feature requests

## 📚 Resources

- [Rust Book](https://doc.rust-lang.org/book/)
- [Tokio Tutorial](https://tokio.rs/tokio/tutorial)
- [Axum Documentation](https://docs.rs/axum/)
- [Project Architecture](docs/ARCHITECTURE.md)
- [API Documentation](docs/API.md)

## 🏷️ Release Process

1. **Version Bumping**: Follow [Semantic Versioning](https://semver.org/)
2. **Changelog**: Update CHANGELOG.md
3. **Testing**: Ensure all tests pass
4. **Documentation**: Update relevant docs
5. **Release**: Create GitHub release with notes

Thank you for contributing to Infinitum Signal! 🚀
