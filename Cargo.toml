[package]
name = "infinitum-signal"
version = "0.1.0"
edition = "2021"
authors = ["Infinitum Signal Team <<EMAIL>>"]
description = "Enterprise Cyber-Compliance Platform with SBOM/HBOM scanning, vulnerability analysis, and blockchain audit trails"
license = "Apache-2.0"
repository = "https://github.com/infinitum-signal/infinitum-signal"
homepage = "https://infinitum-signal.com"
documentation = "https://docs.infinitum-signal.com"
keywords = ["cybersecurity", "compliance", "sbom", "vulnerability", "blockchain"]
categories = ["security", "compliance", "enterprise"]
readme = "README.md"
rust-version = "1.75"

[dependencies]
# Async Runtime
tokio = { version = "1.35", features = ["full"] }
futures = "0.3"
async-trait = "0.1"

# CLI and Configuration
clap = { version = "4.4", features = ["derive", "env"] }
config = "0.14"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"
toml = "0.8"

# API Server
axum = { version = "0.7", features = ["ws", "multipart", "macros"] }
tower = { version = "0.4", features = ["full"] }
tower-http = { version = "0.5", features = ["full"] }
hyper = { version = "1.0", features = ["full"] }

# HTTP Client
reqwest = { version = "0.11", features = ["json", "rustls-tls", "multipart"] }

# Database
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "chrono", "uuid", "json"] }
diesel = { version = "2.1", features = ["postgres", "chrono", "uuid"] }
diesel_migrations = "2.1"
redis = { version = "0.24", features = ["tokio-comp", "connection-manager"] }

# Authentication & Security
jsonwebtoken = "9.2"
argon2 = "0.5"
ring = "0.17"
ed25519-dalek = "2.0"
rand = "0.8"
uuid = { version = "1.6", features = ["v4", "serde"] }

# Cryptography & Blockchain
merkletree = "0.23"
sha2 = "0.10"
blake3 = "1.5"

# Time & Date
chrono = { version = "0.4", features = ["serde"] }
time = "0.3"

# Logging & Observability
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
tracing-appender = "0.2"
prometheus = "0.13"
metrics = "0.22"
metrics-prometheus = "0.6"

# Error Handling
anyhow = "1.0"
thiserror = "1.0"
color-eyre = "0.6"

# File & Path Operations
walkdir = "2.4"
tempfile = "3.8"
tar = "0.4"
flate2 = "1.0"
zip = "0.6"

# Serialization & Formats
bincode = "1.3"
csv = "1.3"
xml-rs = "0.8"

# Compliance & Standards
cyclonedx-bom = "0.5"
spdx = "0.10"

# PDF Generation
printpdf = "0.6"
wkhtmltopdf = "0.5"

# Template Engine
tera = "1.19"
handlebars = "4.5"

# Orchestration & Scheduling
tokio-cron-scheduler = "0.9"
lapin = "2.3"

# Validation
validator = { version = "0.16", features = ["derive"] }
regex = "1.10"

# Utilities
once_cell = "1.19"
lazy_static = "1.4"
itertools = "0.12"
rayon = "1.8"

[dev-dependencies]
# Testing
tokio-test = "0.4"
criterion = { version = "0.5", features = ["html_reports"] }
proptest = "1.4"
mockall = "0.12"
wiremock = "0.5"
testcontainers = "0.15"

# Test Utilities
pretty_assertions = "1.4"
serial_test = "3.0"
rstest = "0.18"

[build-dependencies]
# Build Scripts
vergen = { version = "8.2", features = ["build", "cargo", "git", "rustc"] }

[[bin]]
name = "infinitum-signal"
path = "src/main.rs"

[[bin]]
name = "infinitum-signal-cli"
path = "src/cli.rs"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

[profile.dev]
opt-level = 0
debug = true
split-debuginfo = "unpacked"

[profile.test]
opt-level = 1
debug = true

[workspace]
members = [
    ".",
    "crates/*"
]

[features]
default = ["full"]
full = [
    "sbom-scanning",
    "vulnerability-analysis", 
    "blockchain-integration",
    "compliance-frameworks",
    "ml-features",
    "monitoring"
]
sbom-scanning = []
vulnerability-analysis = []
blockchain-integration = []
compliance-frameworks = []
ml-features = []
monitoring = []
