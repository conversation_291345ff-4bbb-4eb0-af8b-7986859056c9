# Default values for infinitum-signal
# This is a YAML-formatted file.

# Global configuration
global:
  imageRegistry: ""
  imagePullSecrets: []
  storageClass: ""

# Application configuration
image:
  registry: ghcr.io
  repository: infinitum-signal/infinitum-signal
  tag: "0.1.0"
  pullPolicy: IfNotPresent

# Service configuration
service:
  type: ClusterIP
  port: 8080
  targetPort: 8080
  annotations: {}

# Ingress configuration
ingress:
  enabled: false
  className: ""
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: infinitum-signal.local
      paths:
        - path: /
          pathType: Prefix
  tls: []
  #  - secretName: infinitum-signal-tls
  #    hosts:
  #      - infinitum-signal.local

# Deployment configuration
replicaCount: 1

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

# Resource limits
resources:
  limits:
    cpu: 2000m
    memory: 4Gi
  requests:
    cpu: 500m
    memory: 1Gi

# Node selection
nodeSelector: {}
tolerations: []
affinity: {}

# Security context
podSecurityContext:
  fsGroup: 1000
  runAsNonRoot: true
  runAsUser: 1000

securityContext:
  allowPrivilegeEscalation: false
  capabilities:
    drop:
    - ALL
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 1000

# Application configuration
config:
  # Server settings
  server:
    host: "0.0.0.0"
    port: 8080
    workers: 4
    timeout: 30
    
  # Logging
  logging:
    level: info
    format: json
    
  # Security
  security:
    jwtExpiration: 3600
    hashRounds: 12
    
  # Scanning
  scanning:
    timeout: 300
    maxConcurrent: 5
    tempDir: /tmp/scans
    
  # Compliance
  compliance:
    frameworks:
      - cert-in
      - sebi
      - iso27001
    reportRetentionDays: 90

# Environment variables
env:
  - name: RUST_ENV
    value: "production"
  - name: RUST_LOG
    value: "info"

# Secret environment variables
envFrom:
  - secretRef:
      name: infinitum-signal-secrets

# Persistent storage
persistence:
  enabled: true
  storageClass: ""
  accessMode: ReadWriteOnce
  size: 10Gi
  annotations: {}

# PostgreSQL configuration
postgresql:
  enabled: true
  auth:
    postgresPassword: "infinitum-postgres"
    username: "infinitum_user"
    password: "infinitum_pass"
    database: "infinitum_signal"
  primary:
    persistence:
      enabled: true
      size: 20Gi
  metrics:
    enabled: true

# Redis configuration
redis:
  enabled: true
  auth:
    enabled: true
    password: "infinitum-redis"
  master:
    persistence:
      enabled: true
      size: 5Gi
  metrics:
    enabled: true

# Monitoring configuration
monitoring:
  enabled: true
  
  # Prometheus
  prometheus:
    enabled: true
    serviceMonitor:
      enabled: true
      interval: 30s
      scrapeTimeout: 10s
      
  # Grafana
  grafana:
    enabled: true
    adminPassword: "admin"
    dashboards:
      enabled: true
      
  # Metrics
  metrics:
    enabled: true
    port: 9090
    path: /metrics

# Health checks
healthcheck:
  enabled: true
  livenessProbe:
    httpGet:
      path: /health
      port: 8080
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
    
  readinessProbe:
    httpGet:
      path: /ready
      port: 8080
    initialDelaySeconds: 5
    periodSeconds: 5
    timeoutSeconds: 3
    failureThreshold: 3

# Service account
serviceAccount:
  create: true
  annotations: {}
  name: ""

# Pod disruption budget
podDisruptionBudget:
  enabled: false
  minAvailable: 1

# Network policies
networkPolicy:
  enabled: false
  ingress: []
  egress: []

# External secrets
externalSecrets:
  enabled: false
  secretStore:
    name: ""
    kind: SecretStore
  refreshInterval: 1h
  secrets: []

# Backup configuration
backup:
  enabled: false
  schedule: "0 2 * * *"
  retention: 30
  storage:
    type: s3
    bucket: ""
    region: ""

# Migration jobs
migration:
  enabled: true
  image:
    repository: infinitum-signal/migrations
    tag: "0.1.0"
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 100m
      memory: 128Mi
