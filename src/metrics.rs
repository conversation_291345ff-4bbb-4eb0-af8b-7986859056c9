use crate::{config::MetricsConfig, error::Result};
use axum::{
    extract::State,
    http::StatusCode,
    response::{IntoResponse, Response},
    routing::get,
    Router,
};
use metrics::{counter, gauge, histogram, register_counter, register_gauge, register_histogram};
use metrics_prometheus::PrometheusHandle;
use std::{sync::Arc, time::Instant};
use tokio::net::TcpListener;
use tracing::{error, info};

/// Metrics service for Prometheus integration
#[derive(Clone)]
pub struct MetricsService {
    config: Arc<MetricsConfig>,
    prometheus_handle: PrometheusHandle,
}

impl MetricsService {
    pub fn new(config: Arc<MetricsConfig>) -> Result<Self> {
        let prometheus_handle = metrics_prometheus::install();

        // Register default metrics
        register_default_metrics();

        Ok(Self {
            config,
            prometheus_handle,
        })
    }

    /// Start metrics server
    pub async fn start(&self) -> Result<()> {
        if !self.config.enabled {
            info!("Metrics disabled, skipping metrics server");
            return Ok(());
        }

        let addr = format!("0.0.0.0:{}", self.config.port);
        let listener = TcpListener::bind(&addr).await?;

        info!("Metrics server listening on {}", addr);

        let app = Router::new()
            .route(&self.config.path, get(metrics_handler))
            .route("/health", get(health_handler))
            .with_state(self.prometheus_handle.clone());

        axum::serve(listener, app).await?;

        Ok(())
    }

    /// Get Prometheus metrics
    pub fn render(&self) -> String {
        self.prometheus_handle.render()
    }
}

/// Setup metrics based on configuration
pub fn setup_metrics(config: &MetricsConfig) -> Result<()> {
    if !config.enabled {
        info!("Metrics disabled");
        return Ok(());
    }

    info!(
        port = config.port,
        path = %config.path,
        "Metrics enabled"
    );

    Ok(())
}

/// Register default application metrics
fn register_default_metrics() {
    // HTTP metrics
    register_counter!("http_requests_total", "Total number of HTTP requests");
    register_histogram!("http_request_duration_seconds", "HTTP request duration in seconds");
    register_gauge!("http_requests_in_flight", "Number of HTTP requests currently being processed");

    // Scan metrics
    register_counter!("scans_total", "Total number of scans performed");
    register_counter!("scans_failed_total", "Total number of failed scans");
    register_histogram!("scan_duration_seconds", "Scan duration in seconds");
    register_gauge!("scans_in_progress", "Number of scans currently in progress");

    // Vulnerability metrics
    register_counter!("vulnerabilities_found_total", "Total number of vulnerabilities found");
    register_gauge!("vulnerabilities_by_severity", "Number of vulnerabilities by severity");

    // Compliance metrics
    register_counter!("compliance_reports_generated_total", "Total number of compliance reports generated");
    register_gauge!("compliance_issues", "Number of compliance issues");

    // Database metrics
    register_gauge!("database_connections_active", "Number of active database connections");
    register_gauge!("database_connections_idle", "Number of idle database connections");
    register_histogram!("database_query_duration_seconds", "Database query duration in seconds");

    // Cache metrics
    register_counter!("cache_hits_total", "Total number of cache hits");
    register_counter!("cache_misses_total", "Total number of cache misses");

    // System metrics
    register_gauge!("memory_usage_bytes", "Memory usage in bytes");
    register_gauge!("cpu_usage_percent", "CPU usage percentage");
    register_gauge!("disk_usage_bytes", "Disk usage in bytes");
}

/// HTTP request metrics middleware
pub mod middleware {
    use super::*;
    use axum::{
        extract::MatchedPath,
        http::{Request, Response},
        middleware::Next,
        response::IntoResponse,
    };
    use std::time::Instant;

    pub async fn metrics_middleware<B>(
        request: Request<B>,
        next: Next<B>,
    ) -> impl IntoResponse {
        let start = Instant::now();
        let method = request.method().clone();
        let path = request
            .extensions()
            .get::<MatchedPath>()
            .map(|matched_path| matched_path.as_str())
            .unwrap_or_else(|| request.uri().path());

        // Increment in-flight requests
        gauge!("http_requests_in_flight").increment(1.0);

        let response = next.run(request).await;
        let duration = start.elapsed();
        let status = response.status();

        // Record metrics
        counter!("http_requests_total")
            .increment(1, &[
                ("method", method.to_string()),
                ("path", path.to_string()),
                ("status", status.as_u16().to_string()),
            ]);

        histogram!("http_request_duration_seconds")
            .record(duration.as_secs_f64(), &[
                ("method", method.to_string()),
                ("path", path.to_string()),
            ]);

        // Decrement in-flight requests
        gauge!("http_requests_in_flight").decrement(1.0);

        response
    }
}

/// Scan metrics utilities
pub struct ScanMetrics;

impl ScanMetrics {
    pub fn scan_started(scan_type: &str) {
        counter!("scans_total").increment(1, &[("type", scan_type.to_string())]);
        gauge!("scans_in_progress").increment(1.0);
    }

    pub fn scan_completed(scan_type: &str, duration: std::time::Duration) {
        histogram!("scan_duration_seconds")
            .record(duration.as_secs_f64(), &[("type", scan_type.to_string())]);
        gauge!("scans_in_progress").decrement(1.0);
    }

    pub fn scan_failed(scan_type: &str, error_type: &str) {
        counter!("scans_failed_total").increment(1, &[
            ("type", scan_type.to_string()),
            ("error", error_type.to_string()),
        ]);
        gauge!("scans_in_progress").decrement(1.0);
    }

    pub fn vulnerabilities_found(count: u64, severity: &str) {
        counter!("vulnerabilities_found_total")
            .increment(count, &[("severity", severity.to_string())]);
        gauge!("vulnerabilities_by_severity")
            .increment(count as f64, &[("severity", severity.to_string())]);
    }
}

/// Database metrics utilities
pub struct DatabaseMetrics;

impl DatabaseMetrics {
    pub fn record_query_duration(duration: std::time::Duration, operation: &str) {
        histogram!("database_query_duration_seconds")
            .record(duration.as_secs_f64(), &[("operation", operation.to_string())]);
    }

    pub fn update_connection_pool(active: u32, idle: u32) {
        gauge!("database_connections_active").set(active as f64);
        gauge!("database_connections_idle").set(idle as f64);
    }
}

/// Cache metrics utilities
pub struct CacheMetrics;

impl CacheMetrics {
    pub fn cache_hit(cache_type: &str) {
        counter!("cache_hits_total").increment(1, &[("type", cache_type.to_string())]);
    }

    pub fn cache_miss(cache_type: &str) {
        counter!("cache_misses_total").increment(1, &[("type", cache_type.to_string())]);
    }
}

/// Compliance metrics utilities
pub struct ComplianceMetrics;

impl ComplianceMetrics {
    pub fn report_generated(framework: &str) {
        counter!("compliance_reports_generated_total")
            .increment(1, &[("framework", framework.to_string())]);
    }

    pub fn issues_found(count: u64, framework: &str, severity: &str) {
        gauge!("compliance_issues").increment(count as f64, &[
            ("framework", framework.to_string()),
            ("severity", severity.to_string()),
        ]);
    }
}

/// System metrics utilities
pub struct SystemMetrics;

impl SystemMetrics {
    pub fn update_memory_usage(bytes: u64) {
        gauge!("memory_usage_bytes").set(bytes as f64);
    }

    pub fn update_cpu_usage(percent: f64) {
        gauge!("cpu_usage_percent").set(percent);
    }

    pub fn update_disk_usage(bytes: u64) {
        gauge!("disk_usage_bytes").set(bytes as f64);
    }
}

/// Timer for measuring operation duration with automatic metrics recording
pub struct MetricsTimer {
    start: Instant,
    metric_name: String,
    labels: Vec<(String, String)>,
}

impl MetricsTimer {
    pub fn new(metric_name: impl Into<String>) -> Self {
        Self {
            start: Instant::now(),
            metric_name: metric_name.into(),
            labels: Vec::new(),
        }
    }

    pub fn with_label(mut self, key: impl Into<String>, value: impl Into<String>) -> Self {
        self.labels.push((key.into(), value.into()));
        self
    }

    pub fn elapsed(&self) -> std::time::Duration {
        self.start.elapsed()
    }
}

impl Drop for MetricsTimer {
    fn drop(&mut self) {
        let duration = self.elapsed();
        let labels: Vec<(&str, String)> = self
            .labels
            .iter()
            .map(|(k, v)| (k.as_str(), v.clone()))
            .collect();

        histogram!(&self.metric_name).record(duration.as_secs_f64(), &labels);
    }
}

/// Metrics handler for Prometheus endpoint
async fn metrics_handler(State(handle): State<PrometheusHandle>) -> Response {
    match handle.render() {
        Ok(metrics) => (StatusCode::OK, metrics).into_response(),
        Err(err) => {
            error!("Failed to render metrics: {}", err);
            (StatusCode::INTERNAL_SERVER_ERROR, "Failed to render metrics").into_response()
        }
    }
}

/// Health check handler
async fn health_handler() -> Response {
    (StatusCode::OK, "OK").into_response()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_metrics_timer() {
        let timer = MetricsTimer::new("test_metric")
            .with_label("operation", "test")
            .with_label("status", "success");

        std::thread::sleep(std::time::Duration::from_millis(10));
        let elapsed = timer.elapsed();
        assert!(elapsed.as_millis() >= 10);
    }

    #[test]
    fn test_scan_metrics() {
        ScanMetrics::scan_started("sbom");
        ScanMetrics::scan_completed("sbom", std::time::Duration::from_secs(5));
        ScanMetrics::vulnerabilities_found(3, "high");
    }

    #[test]
    fn test_database_metrics() {
        DatabaseMetrics::record_query_duration(std::time::Duration::from_millis(100), "select");
        DatabaseMetrics::update_connection_pool(5, 3);
    }
}
