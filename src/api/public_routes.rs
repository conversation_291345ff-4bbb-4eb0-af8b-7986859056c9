use crate::api::handlers::*;
use axum::{
    routing::{get, post},
    Router,
};

/// Create public API routes
/// These routes are accessible without authentication
pub fn create_routes() -> Router {
    Router::new()
        // Health and status endpoints
        .route("/health", get(get_system_stats))
        .route("/status", get(get_system_stats))
        
        // SBOM operations
        .route("/sbom/upload", post(upload_sbom))
        .route("/scan/sbom", post(scan_sbom))
        .route("/scan/hbom", post(scan_hbom))
        .route("/scan/:scan_id", get(get_scan_results))
        .route("/scans", get(list_scans))
        
        // Vulnerability assessment
        .route("/vulnerability/assess", post(assess_vulnerabilities))
        
        // Compliance reporting
        .route("/compliance/generate", post(generate_compliance_report))
        
        // Blockchain operations (read-only)
        .route("/blockchain/verify", post(verify_blockchain_record))
        
        // Statistics and metrics
        .route("/stats", get(get_system_stats))
        .route("/metrics/summary", get(get_metrics_summary))
}

/// Verify blockchain record (public endpoint)
async fn verify_blockchain_record() -> &'static str {
    "Blockchain verification endpoint - not yet implemented"
}

/// Get metrics summary (public endpoint)
async fn get_metrics_summary() -> &'static str {
    "Metrics summary endpoint - not yet implemented"
}

#[cfg(test)]
mod tests {
    use super::*;
    use axum::http::Method;

    #[test]
    fn test_public_routes_creation() {
        let router = create_routes();
        // Test that router is created successfully
        // In a real test, you'd test the actual routes
    }
}
