//! # API Module
//!
//! This module provides REST and gRPC API endpoints for the Infinitium Signal platform
//! with comprehensive OpenAPI documentation, authentication, and rate limiting.

pub mod docs;
pub mod handlers;
pub mod internal_routes;
pub mod middleware;
pub mod public_routes;
pub mod server;

use crate::{
    config::ApiConfig,
    database::DatabaseService,
    error::{InfinitumError, Result},
};
use axum::{
    extract::{DefaultBodyLimit, State},
    http::{HeaderValue, Method, StatusCode},
    middleware::from_fn_with_state,
    response::Json,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use std::{net::SocketAddr, sync::Arc, time::Duration};
use tower::ServiceBuilder;
use tower_http::{
    cors::{Any, CorsLayer},
    timeout::TimeoutLayer,
    trace::TraceLayer,
};
use tracing::{info, instrument};
use utoipa::OpenApi;

/// Re-export API types
pub use handlers::*;
pub use server::ApiServer;

/// API application state
#[derive(Clone)]
pub struct AppState {
    /// Database service
    pub database: Arc<DatabaseService>,
    /// API configuration
    pub config: ApiConfig,
    /// JWT secret for authentication
    pub jwt_secret: String,
    /// Rate limiter
    pub rate_limiter: Arc<governor::RateLimiter<
        governor::state::NotKeyed,
        governor::state::InMemoryState,
        governor::clock::DefaultClock,
        governor::middleware::NoOpMiddleware,
    >>,
}

/// API response wrapper
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    /// Success status
    pub success: bool,
    /// Response data
    pub data: Option<T>,
    /// Error message if any
    pub error: Option<String>,
    /// Request timestamp
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// Request ID for tracing
    pub request_id: Option<String>,
}

/// API error response
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiError {
    /// Error code
    pub code: String,
    /// Error message
    pub message: String,
    /// Additional details
    pub details: Option<serde_json::Value>,
}

/// Pagination parameters
#[derive(Debug, Deserialize)]
pub struct PaginationParams {
    /// Page number (0-based)
    #[serde(default)]
    pub page: u64,
    /// Items per page (max 100)
    #[serde(default = "default_page_size")]
    pub size: u64,
    /// Sort field
    pub sort: Option<String>,
    /// Sort direction
    pub order: Option<SortOrder>,
}

/// Sort order
#[derive(Debug, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum SortOrder {
    Asc,
    Desc,
}

/// Paginated response
#[derive(Debug, Serialize)]
pub struct PaginatedResponse<T> {
    /// Items in current page
    pub items: Vec<T>,
    /// Current page number
    pub page: u64,
    /// Items per page
    pub size: u64,
    /// Total number of items
    pub total: u64,
    /// Total number of pages
    pub pages: u64,
    /// Has next page
    pub has_next: bool,
    /// Has previous page
    pub has_prev: bool,
}

/// Health check response
#[derive(Debug, Serialize, Deserialize, utoipa::ToSchema)]
pub struct HealthResponse {
    /// Service status
    pub status: String,
    /// Service version
    pub version: String,
    /// Uptime in seconds
    pub uptime: u64,
    /// Database status
    pub database: DatabaseStatus,
    /// Dependencies status
    pub dependencies: DependenciesStatus,
}

/// Database status
#[derive(Debug, Serialize, Deserialize, utoipa::ToSchema)]
pub struct DatabaseStatus {
    /// Connection status
    pub connected: bool,
    /// Response time in milliseconds
    pub response_time_ms: u64,
    /// Pool statistics
    pub pool: PoolStatus,
}

/// Connection pool status
#[derive(Debug, Serialize, Deserialize, utoipa::ToSchema)]
pub struct PoolStatus {
    /// Active connections
    pub active: u32,
    /// Idle connections
    pub idle: u32,
    /// Maximum connections
    pub max: u32,
}

/// Dependencies status
#[derive(Debug, Serialize, Deserialize, utoipa::ToSchema)]
pub struct DependenciesStatus {
    /// Redis status
    pub redis: ServiceStatus,
    /// External APIs status
    pub external_apis: ExternalApisStatus,
}

/// Service status
#[derive(Debug, Serialize, Deserialize, utoipa::ToSchema)]
pub struct ServiceStatus {
    /// Service availability
    pub available: bool,
    /// Response time in milliseconds
    pub response_time_ms: Option<u64>,
    /// Last check timestamp
    pub last_check: chrono::DateTime<chrono::Utc>,
}

/// External APIs status
#[derive(Debug, Serialize, Deserialize, utoipa::ToSchema)]
pub struct ExternalApisStatus {
    /// NVD API status
    pub nvd: ServiceStatus,
    /// Snyk API status
    pub snyk: ServiceStatus,
}

/// JWT claims
#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    /// Subject (user ID)
    pub sub: String,
    /// Issued at
    pub iat: i64,
    /// Expiration time
    pub exp: i64,
    /// User roles
    pub roles: Vec<String>,
    /// API scopes
    pub scopes: Vec<String>,
}

/// Authentication token
#[derive(Debug, Serialize, Deserialize, utoipa::ToSchema)]
pub struct AuthToken {
    /// JWT access token
    pub access_token: String,
    /// Token type (Bearer)
    pub token_type: String,
    /// Expires in seconds
    pub expires_in: u64,
    /// Refresh token
    pub refresh_token: Option<String>,
}

/// Login request
#[derive(Debug, Deserialize, utoipa::ToSchema)]
pub struct LoginRequest {
    /// Username or email
    pub username: String,
    /// Password
    pub password: String,
}

/// API metrics
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiMetrics {
    /// Total requests
    pub total_requests: u64,
    /// Requests per second
    pub requests_per_second: f64,
    /// Average response time
    pub avg_response_time_ms: f64,
    /// Error rate
    pub error_rate: f64,
    /// Active connections
    pub active_connections: u32,
}

impl<T> ApiResponse<T> {
    /// Create successful response
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
            timestamp: chrono::Utc::now(),
            request_id: None,
        }
    }

    /// Create error response
    pub fn error(message: String) -> ApiResponse<()> {
        ApiResponse {
            success: false,
            data: None,
            error: Some(message),
            timestamp: chrono::Utc::now(),
            request_id: None,
        }
    }

    /// Set request ID
    pub fn with_request_id(mut self, request_id: String) -> Self {
        self.request_id = Some(request_id);
        self
    }
}

impl<T> PaginatedResponse<T> {
    /// Create paginated response
    pub fn new(items: Vec<T>, page: u64, size: u64, total: u64) -> Self {
        let pages = (total + size - 1) / size; // Ceiling division
        let has_next = page + 1 < pages;
        let has_prev = page > 0;

        Self {
            items,
            page,
            size,
            total,
            pages,
            has_next,
            has_prev,
        }
    }
}

impl PaginationParams {
    /// Validate pagination parameters
    pub fn validate(&mut self) -> Result<()> {
        if self.size > 100 {
            self.size = 100;
        }
        if self.size == 0 {
            self.size = default_page_size();
        }
        Ok(())
    }

    /// Calculate offset for database queries
    pub fn offset(&self) -> u64 {
        self.page * self.size
    }
}

/// Create the main API router
pub fn create_router(state: AppState) -> Router {
    // Create OpenAPI documentation
    let openapi = docs::ApiDoc::openapi();

    Router::new()
        // Health check endpoint
        .route("/health", get(health_check))
        .route("/metrics", get(get_metrics))
        
        // Authentication endpoints
        .route("/auth/login", post(auth_login))
        .route("/auth/refresh", post(auth_refresh))
        
        // Public API routes
        .nest("/api/v1", public_routes::create_routes())
        
        // Internal API routes (requires authentication)
        .nest("/internal/v1", internal_routes::create_routes())
        
        // OpenAPI documentation
        .route("/docs/openapi.json", get(|| async { Json(openapi) }))
        .merge(utoipa_swagger_ui::SwaggerUi::new("/docs").url("/docs/openapi.json", openapi))
        
        // Apply middleware
        .layer(
            ServiceBuilder::new()
                .layer(TraceLayer::new_for_http())
                .layer(TimeoutLayer::new(Duration::from_secs(30)))
                .layer(DefaultBodyLimit::max(10 * 1024 * 1024)) // 10MB
                .layer(
                    CorsLayer::new()
                        .allow_origin(Any)
                        .allow_methods([Method::GET, Method::POST, Method::PUT, Method::DELETE])
                        .allow_headers(Any),
                )
                .layer(from_fn_with_state(state.clone(), middleware::request_id))
                .layer(from_fn_with_state(state.clone(), middleware::rate_limit))
                .layer(from_fn_with_state(state.clone(), middleware::auth))
        )
        .with_state(state)
}

/// Health check handler
#[utoipa::path(
    get,
    path = "/health",
    responses(
        (status = 200, description = "Service health status", body = HealthResponse)
    )
)]
async fn health_check(State(state): State<AppState>) -> Result<Json<HealthResponse>, StatusCode> {
    let start_time = std::time::Instant::now();
    
    // Check database health
    let db_health = state.database.health_check().await
        .map_err(|_| StatusCode::SERVICE_UNAVAILABLE)?;
    
    let db_response_time = start_time.elapsed().as_millis() as u64;

    let response = HealthResponse {
        status: "healthy".to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        uptime: 0, // TODO: Calculate actual uptime
        database: DatabaseStatus {
            connected: db_health.connected,
            response_time_ms: db_response_time,
            pool: PoolStatus {
                active: db_health.pool_stats.active_connections,
                idle: db_health.pool_stats.idle_connections,
                max: db_health.pool_stats.max_connections,
            },
        },
        dependencies: DependenciesStatus {
            redis: ServiceStatus {
                available: true, // TODO: Check Redis
                response_time_ms: Some(5),
                last_check: chrono::Utc::now(),
            },
            external_apis: ExternalApisStatus {
                nvd: ServiceStatus {
                    available: true, // TODO: Check NVD API
                    response_time_ms: Some(100),
                    last_check: chrono::Utc::now(),
                },
                snyk: ServiceStatus {
                    available: true, // TODO: Check Snyk API
                    response_time_ms: Some(150),
                    last_check: chrono::Utc::now(),
                },
            },
        },
    };

    Ok(Json(response))
}

/// Get API metrics
async fn get_metrics(State(_state): State<AppState>) -> Json<ApiMetrics> {
    // TODO: Implement actual metrics collection
    Json(ApiMetrics {
        total_requests: 1000,
        requests_per_second: 10.5,
        avg_response_time_ms: 150.0,
        error_rate: 0.02,
        active_connections: 25,
    })
}

/// Authentication login handler
async fn auth_login(
    State(_state): State<AppState>,
    Json(_request): Json<LoginRequest>,
) -> Result<Json<AuthToken>, StatusCode> {
    // TODO: Implement actual authentication
    let token = AuthToken {
        access_token: "dummy_token".to_string(),
        token_type: "Bearer".to_string(),
        expires_in: 3600,
        refresh_token: Some("dummy_refresh_token".to_string()),
    };

    Ok(Json(token))
}

/// Authentication refresh handler
async fn auth_refresh(State(_state): State<AppState>) -> Result<Json<AuthToken>, StatusCode> {
    // TODO: Implement token refresh
    let token = AuthToken {
        access_token: "new_dummy_token".to_string(),
        token_type: "Bearer".to_string(),
        expires_in: 3600,
        refresh_token: Some("new_dummy_refresh_token".to_string()),
    };

    Ok(Json(token))
}

/// Default page size for pagination
fn default_page_size() -> u64 {
    20
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_api_response_success() {
        let response = ApiResponse::success("test data");
        assert!(response.success);
        assert_eq!(response.data, Some("test data"));
        assert!(response.error.is_none());
    }

    #[test]
    fn test_api_response_error() {
        let response: ApiResponse<()> = ApiResponse::error("test error".to_string());
        assert!(!response.success);
        assert!(response.data.is_none());
        assert_eq!(response.error, Some("test error".to_string()));
    }

    #[test]
    fn test_pagination_params() {
        let mut params = PaginationParams {
            page: 0,
            size: 150, // Over limit
            sort: None,
            order: None,
        };

        params.validate().unwrap();
        assert_eq!(params.size, 100); // Should be capped
        assert_eq!(params.offset(), 0);
    }

    #[test]
    fn test_paginated_response() {
        let items = vec![1, 2, 3, 4, 5];
        let response = PaginatedResponse::new(items, 0, 5, 25);
        
        assert_eq!(response.page, 0);
        assert_eq!(response.size, 5);
        assert_eq!(response.total, 25);
        assert_eq!(response.pages, 5);
        assert!(response.has_next);
        assert!(!response.has_prev);
    }
}
