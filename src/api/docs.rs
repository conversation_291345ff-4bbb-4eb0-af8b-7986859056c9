use crate::api::{
    handlers::*,
    ApiResponse, HealthResponse, AuthToken, LoginRequest,
    PaginatedResponse, PaginationParams,
};
use utoipa::OpenApi;

/// OpenAPI documentation for Infinitum Signal API
#[derive(OpenApi)]
#[openapi(
    info(
        title = "Infinitum Signal API",
        version = "1.0.0",
        description = "Enterprise Cyber-Compliance Platform API",
        contact(
            name = "Infinitum Signal Team",
            email = "<EMAIL>",
            url = "https://infinitum-signal.com"
        ),
        license(
            name = "Apache 2.0",
            url = "https://www.apache.org/licenses/LICENSE-2.0"
        )
    ),
    servers(
        (url = "http://localhost:8080", description = "Local development server"),
        (url = "https://api.infinitum-signal.com", description = "Production server")
    ),
    paths(
        // Health and status
        crate::api::health_check,
        crate::api::get_metrics,
        
        // Authentication
        crate::api::auth_login,
        crate::api::auth_refresh,
        
        // Scanning endpoints
        scan_sbom,
        scan_hbom,
        get_scan_results,
        list_scans,
        upload_sbom,
        
        // Vulnerability endpoints
        assess_vulnerabilities,
        
        // Compliance endpoints
        generate_compliance_report,
        
        // Blockchain endpoints
        commit_to_blockchain,
        
        // System endpoints
        get_system_stats,
    ),
    components(
        schemas(
            // API response types
            ApiResponse<ScanResponse>,
            ApiResponse<serde_json::Value>,
            PaginatedResponse<ScanResponse>,
            
            // Request/response models
            ScanRequestPayload,
            ScanResponse,
            VulnAssessmentPayload,
            ComplianceReportPayload,
            BlockchainCommitPayload,
            
            // Authentication models
            LoginRequest,
            AuthToken,
            
            // Health and status models
            HealthResponse,
            
            // Pagination
            PaginationParams,
        )
    ),
    tags(
        (name = "health", description = "Health check and system status endpoints"),
        (name = "auth", description = "Authentication and authorization endpoints"),
        (name = "scanning", description = "SBOM/HBOM scanning endpoints"),
        (name = "vulnerability", description = "Vulnerability assessment endpoints"),
        (name = "compliance", description = "Compliance reporting endpoints"),
        (name = "blockchain", description = "Blockchain and audit trail endpoints"),
        (name = "system", description = "System management and statistics endpoints"),
    ),
    security(
        ("bearer_auth" = ["read", "write"]),
        ("api_key" = [])
    )
)]
pub struct ApiDoc;

/// Security scheme definitions
impl ApiDoc {
    /// Get security schemes for OpenAPI
    pub fn security_schemes() -> Vec<(&'static str, utoipa::openapi::security::SecurityScheme)> {
        vec![
            (
                "bearer_auth",
                utoipa::openapi::security::SecurityScheme::Http(
                    utoipa::openapi::security::Http::new(
                        utoipa::openapi::security::HttpAuthScheme::Bearer,
                    )
                    .bearer_format("JWT")
                    .description(Some("JWT Bearer token authentication")),
                ),
            ),
            (
                "api_key",
                utoipa::openapi::security::SecurityScheme::ApiKey(
                    utoipa::openapi::security::ApiKey::Header(
                        utoipa::openapi::security::ApiKeyValue::new("X-API-Key"),
                    )
                    .description(Some("API key authentication")),
                ),
            ),
        ]
    }
}

/// API documentation metadata
pub struct ApiDocumentation {
    /// API title
    pub title: String,
    /// API version
    pub version: String,
    /// API description
    pub description: String,
    /// Base URL
    pub base_url: String,
    /// Contact information
    pub contact: ContactInfo,
    /// License information
    pub license: LicenseInfo,
}

/// Contact information
pub struct ContactInfo {
    /// Contact name
    pub name: String,
    /// Contact email
    pub email: String,
    /// Contact URL
    pub url: String,
}

/// License information
pub struct LicenseInfo {
    /// License name
    pub name: String,
    /// License URL
    pub url: String,
}

impl Default for ApiDocumentation {
    fn default() -> Self {
        Self {
            title: "Infinitum Signal API".to_string(),
            version: env!("CARGO_PKG_VERSION").to_string(),
            description: "Enterprise Cyber-Compliance Platform API providing SBOM/HBOM scanning, vulnerability assessment, compliance reporting, and blockchain audit trails.".to_string(),
            base_url: "http://localhost:8080".to_string(),
            contact: ContactInfo {
                name: "Infinitum Signal Team".to_string(),
                email: "<EMAIL>".to_string(),
                url: "https://infinitum-signal.com".to_string(),
            },
            license: LicenseInfo {
                name: "Apache 2.0".to_string(),
                url: "https://www.apache.org/licenses/LICENSE-2.0".to_string(),
            },
        }
    }
}

/// API endpoint categories
#[derive(Debug, Clone)]
pub enum ApiCategory {
    /// Health and status endpoints
    Health,
    /// Authentication endpoints
    Authentication,
    /// Scanning endpoints
    Scanning,
    /// Vulnerability assessment endpoints
    Vulnerability,
    /// Compliance reporting endpoints
    Compliance,
    /// Blockchain endpoints
    Blockchain,
    /// System management endpoints
    System,
    /// Administrative endpoints
    Admin,
}

impl ApiCategory {
    /// Get category name
    pub fn name(&self) -> &'static str {
        match self {
            ApiCategory::Health => "health",
            ApiCategory::Authentication => "auth",
            ApiCategory::Scanning => "scanning",
            ApiCategory::Vulnerability => "vulnerability",
            ApiCategory::Compliance => "compliance",
            ApiCategory::Blockchain => "blockchain",
            ApiCategory::System => "system",
            ApiCategory::Admin => "admin",
        }
    }

    /// Get category description
    pub fn description(&self) -> &'static str {
        match self {
            ApiCategory::Health => "Health check and system status endpoints",
            ApiCategory::Authentication => "Authentication and authorization endpoints",
            ApiCategory::Scanning => "SBOM/HBOM scanning and analysis endpoints",
            ApiCategory::Vulnerability => "Vulnerability assessment and management endpoints",
            ApiCategory::Compliance => "Compliance reporting and framework endpoints",
            ApiCategory::Blockchain => "Blockchain audit trail and verification endpoints",
            ApiCategory::System => "System management and statistics endpoints",
            ApiCategory::Admin => "Administrative and configuration endpoints",
        }
    }
}

/// API endpoint information
#[derive(Debug, Clone)]
pub struct ApiEndpoint {
    /// HTTP method
    pub method: String,
    /// Endpoint path
    pub path: String,
    /// Endpoint summary
    pub summary: String,
    /// Endpoint description
    pub description: String,
    /// Category
    pub category: ApiCategory,
    /// Authentication required
    pub auth_required: bool,
    /// Required permissions
    pub required_permissions: Vec<String>,
}

impl ApiEndpoint {
    /// Create new API endpoint
    pub fn new(
        method: &str,
        path: &str,
        summary: &str,
        description: &str,
        category: ApiCategory,
        auth_required: bool,
    ) -> Self {
        Self {
            method: method.to_string(),
            path: path.to_string(),
            summary: summary.to_string(),
            description: description.to_string(),
            category,
            auth_required,
            required_permissions: Vec::new(),
        }
    }

    /// Add required permission
    pub fn with_permission(mut self, permission: &str) -> Self {
        self.required_permissions.push(permission.to_string());
        self
    }
}

/// Get all API endpoints
pub fn get_api_endpoints() -> Vec<ApiEndpoint> {
    vec![
        // Health endpoints
        ApiEndpoint::new(
            "GET",
            "/health",
            "Health Check",
            "Get system health status",
            ApiCategory::Health,
            false,
        ),
        ApiEndpoint::new(
            "GET",
            "/metrics",
            "System Metrics",
            "Get system metrics and statistics",
            ApiCategory::Health,
            false,
        ),

        // Authentication endpoints
        ApiEndpoint::new(
            "POST",
            "/auth/login",
            "User Login",
            "Authenticate user and get access token",
            ApiCategory::Authentication,
            false,
        ),
        ApiEndpoint::new(
            "POST",
            "/auth/refresh",
            "Refresh Token",
            "Refresh access token using refresh token",
            ApiCategory::Authentication,
            false,
        ),

        // Scanning endpoints
        ApiEndpoint::new(
            "POST",
            "/api/v1/scan/sbom",
            "Generate SBOM",
            "Generate Software Bill of Materials for a project",
            ApiCategory::Scanning,
            true,
        ).with_permission("scan:create"),
        ApiEndpoint::new(
            "POST",
            "/api/v1/scan/hbom",
            "Generate HBOM",
            "Generate Hardware Bill of Materials for firmware",
            ApiCategory::Scanning,
            true,
        ).with_permission("scan:create"),
        ApiEndpoint::new(
            "GET",
            "/api/v1/scan/{scan_id}",
            "Get Scan Results",
            "Retrieve results of a specific scan",
            ApiCategory::Scanning,
            true,
        ).with_permission("scan:read"),
        ApiEndpoint::new(
            "GET",
            "/api/v1/scans",
            "List Scans",
            "List all scans with pagination",
            ApiCategory::Scanning,
            true,
        ).with_permission("scan:read"),

        // Vulnerability endpoints
        ApiEndpoint::new(
            "POST",
            "/api/v1/vulnerability/assess",
            "Assess Vulnerabilities",
            "Perform vulnerability assessment on SBOM or target",
            ApiCategory::Vulnerability,
            true,
        ).with_permission("vuln:assess"),

        // Compliance endpoints
        ApiEndpoint::new(
            "POST",
            "/api/v1/compliance/generate",
            "Generate Compliance Report",
            "Generate compliance report for specified framework",
            ApiCategory::Compliance,
            true,
        ).with_permission("compliance:generate"),

        // Blockchain endpoints
        ApiEndpoint::new(
            "POST",
            "/api/v1/blockchain/commit",
            "Commit to Blockchain",
            "Commit data to blockchain for audit trail",
            ApiCategory::Blockchain,
            true,
        ).with_permission("blockchain:commit"),

        // System endpoints
        ApiEndpoint::new(
            "GET",
            "/api/v1/stats",
            "System Statistics",
            "Get detailed system statistics",
            ApiCategory::System,
            true,
        ).with_permission("system:read"),
    ]
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_api_documentation_default() {
        let docs = ApiDocumentation::default();
        assert_eq!(docs.title, "Infinitum Signal API");
        assert!(!docs.version.is_empty());
        assert!(!docs.description.is_empty());
    }

    #[test]
    fn test_api_category() {
        let category = ApiCategory::Scanning;
        assert_eq!(category.name(), "scanning");
        assert!(!category.description().is_empty());
    }

    #[test]
    fn test_api_endpoint() {
        let endpoint = ApiEndpoint::new(
            "GET",
            "/test",
            "Test Endpoint",
            "Test description",
            ApiCategory::Health,
            false,
        ).with_permission("test:read");

        assert_eq!(endpoint.method, "GET");
        assert_eq!(endpoint.path, "/test");
        assert!(!endpoint.auth_required);
        assert_eq!(endpoint.required_permissions.len(), 1);
    }

    #[test]
    fn test_get_api_endpoints() {
        let endpoints = get_api_endpoints();
        assert!(!endpoints.is_empty());
        
        // Check that we have endpoints for each category
        let categories: std::collections::HashSet<_> = endpoints
            .iter()
            .map(|e| e.category.name())
            .collect();
        
        assert!(categories.contains("health"));
        assert!(categories.contains("scanning"));
        assert!(categories.contains("vulnerability"));
    }
}
