use crate::{
    config::BlockchainConfig,
    error::Result,
    blockchain::{BlockchainRecord, AuditTrail},
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{debug, info, instrument};

/// Blockchain commit service for immutable record keeping
pub struct CommitService {
    config: BlockchainConfig,
    chain_storage: ChainStorage,
}

/// Commit proof for blockchain records
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommitProof {
    /// Record hash
    pub record_hash: String,
    /// Block hash containing the record
    pub block_hash: String,
    /// Block height
    pub block_height: u64,
    /// Merkle proof
    pub merkle_proof: Vec<String>,
    /// Timestamp
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// Digital signature
    pub signature: String,
}

/// Blockchain block structure
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Block {
    /// Block header
    pub header: BlockHeader,
    /// Transactions in the block
    pub transactions: Vec<Transaction>,
    /// Block hash
    pub hash: String,
}

/// Block header
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct BlockHeader {
    /// Block height
    pub height: u64,
    /// Previous block hash
    pub previous_hash: String,
    /// Merkle root of transactions
    pub merkle_root: String,
    /// Block timestamp
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// Block nonce (for proof of work, if applicable)
    pub nonce: u64,
    /// Block version
    pub version: u32,
}

/// Transaction structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Transaction {
    /// Transaction ID
    pub id: String,
    /// Transaction type
    pub tx_type: String,
    /// Transaction data hash
    pub data_hash: String,
    /// Digital signature
    pub signature: String,
    /// Public key
    pub public_key: String,
    /// Timestamp
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// Metadata
    pub metadata: HashMap<String, String>,
}

/// Chain storage interface
pub struct ChainStorage {
    /// Storage backend type
    backend: StorageBackend,
    /// Storage path
    storage_path: String,
}

/// Storage backend types
#[derive(Debug, Clone)]
pub enum StorageBackend {
    /// File system storage
    FileSystem,
    /// Database storage
    Database,
    /// IPFS storage
    Ipfs,
    /// Hyperledger Fabric
    HyperledgerFabric,
}

/// Chain statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChainStatistics {
    /// Total blocks
    pub total_blocks: u64,
    /// Total transactions
    pub total_transactions: u64,
    /// Chain size in bytes
    pub chain_size_bytes: u64,
    /// Average block time
    pub avg_block_time_seconds: f64,
    /// Last block timestamp
    pub last_block_timestamp: chrono::DateTime<chrono::Utc>,
}

impl CommitService {
    /// Create new commit service
    pub fn new(config: &BlockchainConfig) -> Self {
        let chain_storage = ChainStorage::new(&config.storage_backend, &config.storage_path);
        
        Self {
            config: config.clone(),
            chain_storage,
        }
    }

    /// Commit a record to the blockchain
    #[instrument(skip(self, record))]
    pub async fn commit_record(&self, record: &BlockchainRecord) -> Result<CommitProof> {
        info!("Committing record to blockchain: {}", record.id);

        // Create transaction from record
        let transaction = self.create_transaction(record).await?;

        // Add transaction to pending pool
        self.add_to_pending_pool(&transaction).await?;

        // Create new block (simplified - in reality, this would be done by miners/validators)
        let block = self.create_block(vec![transaction]).await?;

        // Store block in chain
        self.chain_storage.store_block(&block).await?;

        // Generate commit proof
        let proof = self.generate_commit_proof(record, &block).await?;

        debug!("Record committed successfully with proof: {}", proof.record_hash);
        Ok(proof)
    }

    /// Get audit trail for a subject
    #[instrument(skip(self))]
    pub async fn get_audit_trail(&self, subject_id: &str) -> Result<AuditTrail> {
        info!("Retrieving audit trail for subject: {}", subject_id);

        let records = self.chain_storage.get_records_by_subject(subject_id).await?;
        let chain_stats = self.chain_storage.get_chain_statistics().await?;

        let audit_trail = AuditTrail {
            chain_id: self.config.chain_id.clone(),
            genesis_hash: self.get_genesis_hash().await?,
            block_height: chain_stats.total_blocks,
            records,
            integrity_verified: self.verify_chain_integrity().await?,
        };

        Ok(audit_trail)
    }

    /// Verify the integrity of a record
    pub async fn verify_record(&self, record: &BlockchainRecord) -> Result<bool> {
        // Get the block containing this record
        let block = self.chain_storage.get_block_by_record_hash(&record.data_hash).await?;
        
        if let Some(block) = block {
            // Verify the record is in the block
            let record_in_block = block.transactions.iter()
                .any(|tx| tx.data_hash == record.data_hash);
            
            if !record_in_block {
                return Ok(false);
            }

            // Verify block integrity
            let calculated_hash = self.calculate_block_hash(&block)?;
            if calculated_hash != block.hash {
                return Ok(false);
            }

            // Verify digital signature
            self.verify_record_signature(record).await
        } else {
            Ok(false)
        }
    }

    /// Create transaction from blockchain record
    async fn create_transaction(&self, record: &BlockchainRecord) -> Result<Transaction> {
        Ok(Transaction {
            id: uuid::Uuid::new_v4().to_string(),
            tx_type: format!("{:?}", record.transaction_type),
            data_hash: record.data_hash.clone(),
            signature: record.signature.clone(),
            public_key: record.public_key.clone(),
            timestamp: record.timestamp,
            metadata: record.metadata.clone(),
        })
    }

    /// Add transaction to pending pool
    async fn add_to_pending_pool(&self, _transaction: &Transaction) -> Result<()> {
        // In a real implementation, this would add to a transaction pool
        // For now, we'll just log it
        debug!("Transaction added to pending pool");
        Ok(())
    }

    /// Create a new block
    async fn create_block(&self, transactions: Vec<Transaction>) -> Result<Block> {
        let previous_block = self.chain_storage.get_latest_block().await?;
        let height = previous_block.map(|b| b.header.height + 1).unwrap_or(0);
        let previous_hash = previous_block.map(|b| b.hash).unwrap_or_else(|| "0".repeat(64));

        // Calculate Merkle root
        let merkle_root = self.calculate_merkle_root(&transactions)?;

        let header = BlockHeader {
            height,
            previous_hash,
            merkle_root,
            timestamp: chrono::Utc::now(),
            nonce: 0, // Simplified - no proof of work
            version: 1,
        };

        let block = Block {
            header: header.clone(),
            transactions,
            hash: String::new(), // Will be calculated
        };

        // Calculate block hash
        let block_hash = self.calculate_block_hash(&block)?;
        
        Ok(Block {
            hash: block_hash,
            ..block
        })
    }

    /// Generate commit proof
    async fn generate_commit_proof(&self, record: &BlockchainRecord, block: &Block) -> Result<CommitProof> {
        // Generate Merkle proof for the record in the block
        let merkle_proof = self.generate_merkle_proof(record, block)?;

        Ok(CommitProof {
            record_hash: record.data_hash.clone(),
            block_hash: block.hash.clone(),
            block_height: block.header.height,
            merkle_proof,
            timestamp: chrono::Utc::now(),
            signature: record.signature.clone(),
        })
    }

    /// Calculate Merkle root of transactions
    fn calculate_merkle_root(&self, transactions: &[Transaction]) -> Result<String> {
        if transactions.is_empty() {
            return Ok("0".repeat(64));
        }

        let mut hashes: Vec<String> = transactions.iter()
            .map(|tx| tx.data_hash.clone())
            .collect();

        while hashes.len() > 1 {
            let mut next_level = Vec::new();
            
            for chunk in hashes.chunks(2) {
                let combined = if chunk.len() == 2 {
                    format!("{}{}", chunk[0], chunk[1])
                } else {
                    format!("{}{}", chunk[0], chunk[0]) // Duplicate if odd number
                };
                
                let hash = sha2::Sha256::digest(combined.as_bytes());
                next_level.push(hex::encode(hash));
            }
            
            hashes = next_level;
        }

        Ok(hashes[0].clone())
    }

    /// Calculate block hash
    fn calculate_block_hash(&self, block: &Block) -> Result<String> {
        let header_json = serde_json::to_string(&block.header)?;
        let transactions_json = serde_json::to_string(&block.transactions)?;
        let combined = format!("{}{}", header_json, transactions_json);
        
        let hash = sha2::Sha256::digest(combined.as_bytes());
        Ok(hex::encode(hash))
    }

    /// Generate Merkle proof for a record
    fn generate_merkle_proof(&self, record: &BlockchainRecord, block: &Block) -> Result<Vec<String>> {
        // Find the transaction containing this record
        let tx_index = block.transactions.iter()
            .position(|tx| tx.data_hash == record.data_hash)
            .ok_or_else(|| crate::error::InfinitumError::Internal {
                message: "Record not found in block".to_string(),
            })?;

        // Generate Merkle proof (simplified implementation)
        let mut proof = Vec::new();
        let mut current_index = tx_index;
        let mut level_hashes: Vec<String> = block.transactions.iter()
            .map(|tx| tx.data_hash.clone())
            .collect();

        while level_hashes.len() > 1 {
            let sibling_index = if current_index % 2 == 0 {
                current_index + 1
            } else {
                current_index - 1
            };

            if sibling_index < level_hashes.len() {
                proof.push(level_hashes[sibling_index].clone());
            }

            // Move to next level
            let mut next_level = Vec::new();
            for chunk in level_hashes.chunks(2) {
                let combined = if chunk.len() == 2 {
                    format!("{}{}", chunk[0], chunk[1])
                } else {
                    format!("{}{}", chunk[0], chunk[0])
                };
                let hash = sha2::Sha256::digest(combined.as_bytes());
                next_level.push(hex::encode(hash));
            }

            level_hashes = next_level;
            current_index /= 2;
        }

        Ok(proof)
    }

    /// Get genesis block hash
    async fn get_genesis_hash(&self) -> Result<String> {
        if let Some(genesis_block) = self.chain_storage.get_block_by_height(0).await? {
            Ok(genesis_block.hash)
        } else {
            Ok("0".repeat(64)) // No genesis block yet
        }
    }

    /// Verify chain integrity
    async fn verify_chain_integrity(&self) -> Result<bool> {
        let stats = self.chain_storage.get_chain_statistics().await?;
        
        // Verify each block links to the previous one
        for height in 1..stats.total_blocks {
            let current_block = self.chain_storage.get_block_by_height(height).await?;
            let previous_block = self.chain_storage.get_block_by_height(height - 1).await?;

            if let (Some(current), Some(previous)) = (current_block, previous_block) {
                if current.header.previous_hash != previous.hash {
                    return Ok(false);
                }
            } else {
                return Ok(false);
            }
        }

        Ok(true)
    }

    /// Verify record signature
    async fn verify_record_signature(&self, record: &BlockchainRecord) -> Result<bool> {
        use ed25519_dalek::{Signature, Verifier, VerifyingKey};
        
        let signature_bytes = hex::decode(&record.signature)
            .map_err(|e| crate::error::InfinitumError::Internal { 
                message: format!("Invalid signature hex: {}", e) 
            })?;
        
        let public_key_bytes = hex::decode(&record.public_key)
            .map_err(|e| crate::error::InfinitumError::Internal { 
                message: format!("Invalid public key hex: {}", e) 
            })?;
        
        let signature = Signature::from_bytes(&signature_bytes.try_into().map_err(|_| {
            crate::error::InfinitumError::Internal { 
                message: "Invalid signature length".to_string() 
            }
        })?);
        
        let public_key = VerifyingKey::from_bytes(&public_key_bytes.try_into().map_err(|_| {
            crate::error::InfinitumError::Internal { 
                message: "Invalid public key length".to_string() 
            }
        })?).map_err(|e| crate::error::InfinitumError::Internal { 
            message: format!("Invalid public key: {}", e) 
        })?;
        
        Ok(public_key.verify(record.data_hash.as_bytes(), &signature).is_ok())
    }
}

impl ChainStorage {
    /// Create new chain storage
    pub fn new(backend: &str, storage_path: &str) -> Self {
        let backend = match backend.to_lowercase().as_str() {
            "filesystem" => StorageBackend::FileSystem,
            "database" => StorageBackend::Database,
            "ipfs" => StorageBackend::Ipfs,
            "hyperledger" => StorageBackend::HyperledgerFabric,
            _ => StorageBackend::FileSystem,
        };

        Self {
            backend,
            storage_path: storage_path.to_string(),
        }
    }

    /// Store a block
    pub async fn store_block(&self, block: &Block) -> Result<()> {
        match self.backend {
            StorageBackend::FileSystem => {
                let block_path = format!("{}/block_{}.json", self.storage_path, block.header.height);
                let block_json = serde_json::to_string_pretty(block)?;
                tokio::fs::create_dir_all(&self.storage_path).await?;
                tokio::fs::write(block_path, block_json).await?;
            }
            _ => {
                // TODO: Implement other storage backends
                debug!("Storage backend {:?} not yet implemented", self.backend);
            }
        }
        Ok(())
    }

    /// Get latest block
    pub async fn get_latest_block(&self) -> Result<Option<Block>> {
        match self.backend {
            StorageBackend::FileSystem => {
                // Find the highest numbered block file
                let mut entries = tokio::fs::read_dir(&self.storage_path).await?;
                let mut max_height = None;

                while let Some(entry) = entries.next_entry().await? {
                    let file_name = entry.file_name();
                    let file_name_str = file_name.to_string_lossy();
                    
                    if file_name_str.starts_with("block_") && file_name_str.ends_with(".json") {
                        if let Some(height_str) = file_name_str.strip_prefix("block_").and_then(|s| s.strip_suffix(".json")) {
                            if let Ok(height) = height_str.parse::<u64>() {
                                max_height = Some(max_height.unwrap_or(0).max(height));
                            }
                        }
                    }
                }

                if let Some(height) = max_height {
                    self.get_block_by_height(height).await
                } else {
                    Ok(None)
                }
            }
            _ => Ok(None),
        }
    }

    /// Get block by height
    pub async fn get_block_by_height(&self, height: u64) -> Result<Option<Block>> {
        match self.backend {
            StorageBackend::FileSystem => {
                let block_path = format!("{}/block_{}.json", self.storage_path, height);
                
                if tokio::fs::metadata(&block_path).await.is_ok() {
                    let block_json = tokio::fs::read_to_string(block_path).await?;
                    let block: Block = serde_json::from_str(&block_json)?;
                    Ok(Some(block))
                } else {
                    Ok(None)
                }
            }
            _ => Ok(None),
        }
    }

    /// Get block by record hash
    pub async fn get_block_by_record_hash(&self, _record_hash: &str) -> Result<Option<Block>> {
        // TODO: Implement efficient lookup by record hash
        // For now, return None
        Ok(None)
    }

    /// Get records by subject
    pub async fn get_records_by_subject(&self, _subject_id: &str) -> Result<Vec<crate::blockchain::BlockchainRecord>> {
        // TODO: Implement subject-based record lookup
        Ok(Vec::new())
    }

    /// Get chain statistics
    pub async fn get_chain_statistics(&self) -> Result<ChainStatistics> {
        match self.backend {
            StorageBackend::FileSystem => {
                let mut total_blocks = 0;
                let mut total_transactions = 0;
                let mut chain_size_bytes = 0;
                let mut last_block_timestamp = chrono::Utc::now();

                if let Ok(mut entries) = tokio::fs::read_dir(&self.storage_path).await {
                    while let Some(entry) = entries.next_entry().await? {
                        let file_name = entry.file_name();
                        let file_name_str = file_name.to_string_lossy();
                        
                        if file_name_str.starts_with("block_") && file_name_str.ends_with(".json") {
                            total_blocks += 1;
                            
                            if let Ok(metadata) = entry.metadata().await {
                                chain_size_bytes += metadata.len();
                            }

                            // Read block to get transaction count and timestamp
                            if let Ok(block_json) = tokio::fs::read_to_string(entry.path()).await {
                                if let Ok(block) = serde_json::from_str::<Block>(&block_json) {
                                    total_transactions += block.transactions.len() as u64;
                                    last_block_timestamp = block.header.timestamp;
                                }
                            }
                        }
                    }
                }

                Ok(ChainStatistics {
                    total_blocks,
                    total_transactions,
                    chain_size_bytes,
                    avg_block_time_seconds: 60.0, // Default 1 minute
                    last_block_timestamp,
                })
            }
            _ => Ok(ChainStatistics {
                total_blocks: 0,
                total_transactions: 0,
                chain_size_bytes: 0,
                avg_block_time_seconds: 0.0,
                last_block_timestamp: chrono::Utc::now(),
            }),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_merkle_root_calculation() {
        let config = BlockchainConfig::default();
        let service = CommitService::new(&config);

        let transactions = vec![
            Transaction {
                id: "tx1".to_string(),
                tx_type: "test".to_string(),
                data_hash: "hash1".to_string(),
                signature: "sig1".to_string(),
                public_key: "key1".to_string(),
                timestamp: chrono::Utc::now(),
                metadata: HashMap::new(),
            },
            Transaction {
                id: "tx2".to_string(),
                tx_type: "test".to_string(),
                data_hash: "hash2".to_string(),
                signature: "sig2".to_string(),
                public_key: "key2".to_string(),
                timestamp: chrono::Utc::now(),
                metadata: HashMap::new(),
            },
        ];

        let merkle_root = service.calculate_merkle_root(&transactions).unwrap();
        assert!(!merkle_root.is_empty());
        assert_eq!(merkle_root.len(), 64); // SHA-256 hex string
    }

    #[test]
    fn test_chain_storage_creation() {
        let storage = ChainStorage::new("filesystem", "/tmp/test_chain");
        assert_eq!(storage.storage_path, "/tmp/test_chain");
    }
}
