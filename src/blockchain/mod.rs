//! # Blockchain Module
//!
//! This module provides blockchain-based audit trails, verifiable credentials,
//! and immutable record keeping for compliance and security attestation.

pub mod commit;
pub mod merkle_proof;
pub mod verifiable_credential;

use crate::{
    config::BlockchainConfig,
    error::{InfinitumError, Result},
    scanners::ScanResult,
    compliance::ComplianceReport,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;

/// Re-export blockchain types
pub use commit::{BlockchainCommit, CommitProof};
pub use merkle_proof::{MerkleProof, MerkleTree};
pub use verifiable_credential::{VerifiableCredential, CredentialSubject};

/// Blockchain transaction types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum TransactionType {
    /// SBOM/HBOM scan result
    ScanResult,
    /// Compliance report
    ComplianceReport,
    /// Vulnerability assessment
    VulnerabilityAssessment,
    /// Audit log entry
    AuditLog,
    /// Certificate issuance
    Certificate,
    /// Policy update
    PolicyUpdate,
}

/// Blockchain record for audit trail
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BlockchainRecord {
    /// Unique record identifier
    pub id: Uuid,
    /// Transaction type
    pub transaction_type: TransactionType,
    /// Record timestamp
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// Data hash (SHA-256)
    pub data_hash: String,
    /// Previous record hash (for chaining)
    pub previous_hash: Option<String>,
    /// Digital signature
    pub signature: String,
    /// Public key used for signing
    pub public_key: String,
    /// Metadata
    pub metadata: HashMap<String, String>,
    /// Merkle proof for data integrity
    pub merkle_proof: Option<MerkleProof>,
}

/// Blockchain audit trail
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuditTrail {
    /// Chain identifier
    pub chain_id: String,
    /// Genesis block hash
    pub genesis_hash: String,
    /// Current block height
    pub block_height: u64,
    /// Records in the chain
    pub records: Vec<BlockchainRecord>,
    /// Chain integrity status
    pub integrity_verified: bool,
}

/// Digital signature for blockchain records
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DigitalSignature {
    /// Signature algorithm
    pub algorithm: SignatureAlgorithm,
    /// Signature value
    pub signature: Vec<u8>,
    /// Public key
    pub public_key: Vec<u8>,
    /// Signing timestamp
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// Supported signature algorithms
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum SignatureAlgorithm {
    /// Ed25519 (recommended)
    Ed25519,
    /// ECDSA with P-256
    EcdsaP256,
    /// RSA with SHA-256
    RsaSha256,
}

/// Blockchain attestation for compliance
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceAttestation {
    /// Attestation identifier
    pub id: Uuid,
    /// Subject (what is being attested)
    pub subject: String,
    /// Compliance framework
    pub framework: String,
    /// Attestation claims
    pub claims: Vec<AttestationClaim>,
    /// Evidence references
    pub evidence: Vec<String>,
    /// Issuer information
    pub issuer: AttestationIssuer,
    /// Validity period
    pub valid_from: chrono::DateTime<chrono::Utc>,
    pub valid_until: chrono::DateTime<chrono::Utc>,
    /// Digital signature
    pub signature: DigitalSignature,
}

/// Individual attestation claim
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AttestationClaim {
    /// Claim type
    pub claim_type: String,
    /// Claim value
    pub value: serde_json::Value,
    /// Confidence level (0.0 - 1.0)
    pub confidence: f64,
    /// Supporting evidence
    pub evidence: Vec<String>,
}

/// Attestation issuer information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AttestationIssuer {
    /// Issuer identifier
    pub id: String,
    /// Issuer name
    pub name: String,
    /// Issuer public key
    pub public_key: String,
    /// Issuer credentials
    pub credentials: Vec<String>,
}

/// Blockchain orchestrator
pub struct BlockchainOrchestrator {
    config: BlockchainConfig,
    commit_service: commit::CommitService,
    merkle_service: merkle_proof::MerkleService,
    credential_service: verifiable_credential::CredentialService,
    signing_key: ed25519_dalek::SigningKey,
    verifying_key: ed25519_dalek::VerifyingKey,
}

impl BlockchainOrchestrator {
    /// Create new blockchain orchestrator
    pub fn new(config: BlockchainConfig) -> Result<Self> {
        // Generate or load signing keys
        let signing_key = ed25519_dalek::SigningKey::generate(&mut rand::rngs::OsRng);
        let verifying_key = signing_key.verifying_key();

        Ok(Self {
            commit_service: commit::CommitService::new(&config),
            merkle_service: merkle_proof::MerkleService::new(&config),
            credential_service: verifiable_credential::CredentialService::new(&config),
            signing_key,
            verifying_key,
            config,
        })
    }

    /// Commit scan result to blockchain
    pub async fn commit_scan_result(&self, scan_result: &ScanResult) -> Result<BlockchainRecord> {
        let data_hash = self.calculate_hash(scan_result)?;
        let signature = self.sign_data(&data_hash)?;

        let record = BlockchainRecord {
            id: Uuid::new_v4(),
            transaction_type: TransactionType::ScanResult,
            timestamp: chrono::Utc::now(),
            data_hash,
            previous_hash: None, // TODO: Get from chain
            signature,
            public_key: hex::encode(self.verifying_key.as_bytes()),
            metadata: {
                let mut meta = HashMap::new();
                meta.insert("scan_id".to_string(), scan_result.request.id.to_string());
                meta.insert("scan_type".to_string(), scan_result.request.scan_type.to_string());
                meta
            },
            merkle_proof: None,
        };

        // Commit to blockchain
        self.commit_service.commit_record(&record).await?;

        Ok(record)
    }

    /// Commit compliance report to blockchain
    pub async fn commit_compliance_report(&self, report: &ComplianceReport) -> Result<BlockchainRecord> {
        let data_hash = self.calculate_hash(report)?;
        let signature = self.sign_data(&data_hash)?;

        let record = BlockchainRecord {
            id: Uuid::new_v4(),
            transaction_type: TransactionType::ComplianceReport,
            timestamp: chrono::Utc::now(),
            data_hash,
            previous_hash: None, // TODO: Get from chain
            signature,
            public_key: hex::encode(self.verifying_key.as_bytes()),
            metadata: {
                let mut meta = HashMap::new();
                meta.insert("report_id".to_string(), report.request.id.to_string());
                meta.insert("framework".to_string(), format!("{:?}", report.request.framework));
                meta.insert("compliance_score".to_string(), report.summary.compliance_score.to_string());
                meta
            },
            merkle_proof: None,
        };

        // Commit to blockchain
        self.commit_service.commit_record(&record).await?;

        Ok(record)
    }

    /// Generate verifiable credential for compliance
    pub async fn issue_compliance_credential(
        &self,
        report: &ComplianceReport,
    ) -> Result<VerifiableCredential> {
        self.credential_service.issue_compliance_credential(report).await
    }

    /// Verify blockchain record integrity
    pub async fn verify_record(&self, record: &BlockchainRecord) -> Result<bool> {
        // Verify signature
        let signature_valid = self.verify_signature(&record.data_hash, &record.signature, &record.public_key)?;
        
        if !signature_valid {
            return Ok(false);
        }

        // Verify Merkle proof if present
        if let Some(merkle_proof) = &record.merkle_proof {
            let merkle_valid = self.merkle_service.verify_proof(merkle_proof, &record.data_hash).await?;
            if !merkle_valid {
                return Ok(false);
            }
        }

        Ok(true)
    }

    /// Get audit trail for a specific subject
    pub async fn get_audit_trail(&self, subject_id: &str) -> Result<AuditTrail> {
        self.commit_service.get_audit_trail(subject_id).await
    }

    /// Generate compliance attestation
    pub async fn generate_attestation(
        &self,
        subject: &str,
        framework: &str,
        claims: Vec<AttestationClaim>,
    ) -> Result<ComplianceAttestation> {
        let attestation_id = Uuid::new_v4();
        let now = chrono::Utc::now();
        
        let attestation = ComplianceAttestation {
            id: attestation_id,
            subject: subject.to_string(),
            framework: framework.to_string(),
            claims,
            evidence: Vec::new(),
            issuer: AttestationIssuer {
                id: "infinitum-signal".to_string(),
                name: "Infinitium Signal Platform".to_string(),
                public_key: hex::encode(self.verifying_key.as_bytes()),
                credentials: vec!["cybersecurity-platform".to_string()],
            },
            valid_from: now,
            valid_until: now + chrono::Duration::days(365),
            signature: self.create_signature(&attestation_id.to_string())?,
        };

        Ok(attestation)
    }

    /// Calculate hash of data
    fn calculate_hash<T: Serialize>(&self, data: &T) -> Result<String> {
        let json_data = serde_json::to_string(data)?;
        let hash = sha2::Sha256::digest(json_data.as_bytes());
        Ok(hex::encode(hash))
    }

    /// Sign data with private key
    fn sign_data(&self, data: &str) -> Result<String> {
        use ed25519_dalek::Signer;
        let signature = self.signing_key.sign(data.as_bytes());
        Ok(hex::encode(signature.to_bytes()))
    }

    /// Verify signature
    fn verify_signature(&self, data: &str, signature_hex: &str, public_key_hex: &str) -> Result<bool> {
        use ed25519_dalek::{Signature, Verifier, VerifyingKey};
        
        let signature_bytes = hex::decode(signature_hex)
            .map_err(|e| InfinitumError::Internal { message: format!("Invalid signature hex: {}", e) })?;
        
        let public_key_bytes = hex::decode(public_key_hex)
            .map_err(|e| InfinitumError::Internal { message: format!("Invalid public key hex: {}", e) })?;
        
        let signature = Signature::from_bytes(&signature_bytes.try_into().map_err(|_| {
            InfinitumError::Internal { message: "Invalid signature length".to_string() }
        })?);
        
        let public_key = VerifyingKey::from_bytes(&public_key_bytes.try_into().map_err(|_| {
            InfinitumError::Internal { message: "Invalid public key length".to_string() }
        })?).map_err(|e| InfinitumError::Internal { message: format!("Invalid public key: {}", e) })?;
        
        Ok(public_key.verify(data.as_bytes(), &signature).is_ok())
    }

    /// Create digital signature
    fn create_signature(&self, data: &str) -> Result<DigitalSignature> {
        use ed25519_dalek::Signer;
        let signature = self.signing_key.sign(data.as_bytes());
        
        Ok(DigitalSignature {
            algorithm: SignatureAlgorithm::Ed25519,
            signature: signature.to_bytes().to_vec(),
            public_key: self.verifying_key.as_bytes().to_vec(),
            timestamp: chrono::Utc::now(),
        })
    }
}

/// Blockchain utilities
pub mod utils {
    use super::*;

    /// Generate blockchain address from public key
    pub fn generate_address(public_key: &[u8]) -> String {
        let hash = sha2::Sha256::digest(public_key);
        hex::encode(&hash[..20]) // Take first 20 bytes
    }

    /// Validate blockchain record format
    pub fn validate_record(record: &BlockchainRecord) -> Result<()> {
        // Validate required fields
        if record.data_hash.is_empty() {
            return Err(InfinitumError::Validation {
                field: "data_hash".to_string(),
                message: "Data hash cannot be empty".to_string(),
            });
        }

        if record.signature.is_empty() {
            return Err(InfinitumError::Validation {
                field: "signature".to_string(),
                message: "Signature cannot be empty".to_string(),
            });
        }

        if record.public_key.is_empty() {
            return Err(InfinitumError::Validation {
                field: "public_key".to_string(),
                message: "Public key cannot be empty".to_string(),
            });
        }

        // Validate hex encoding
        hex::decode(&record.data_hash).map_err(|_| InfinitumError::Validation {
            field: "data_hash".to_string(),
            message: "Invalid hex encoding".to_string(),
        })?;

        hex::decode(&record.signature).map_err(|_| InfinitumError::Validation {
            field: "signature".to_string(),
            message: "Invalid hex encoding".to_string(),
        })?;

        hex::decode(&record.public_key).map_err(|_| InfinitumError::Validation {
            field: "public_key".to_string(),
            message: "Invalid hex encoding".to_string(),
        })?;

        Ok(())
    }

    /// Calculate record size in bytes
    pub fn calculate_record_size(record: &BlockchainRecord) -> usize {
        serde_json::to_string(record)
            .map(|s| s.len())
            .unwrap_or(0)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_transaction_type_serialization() {
        let tx_type = TransactionType::ScanResult;
        let serialized = serde_json::to_string(&tx_type).unwrap();
        assert_eq!(serialized, "\"scan_result\"");
    }

    #[test]
    fn test_signature_algorithm() {
        assert_eq!(SignatureAlgorithm::Ed25519, SignatureAlgorithm::Ed25519);
        assert_ne!(SignatureAlgorithm::Ed25519, SignatureAlgorithm::EcdsaP256);
    }

    #[test]
    fn test_address_generation() {
        let public_key = b"test_public_key_32_bytes_long!!!";
        let address = utils::generate_address(public_key);
        assert_eq!(address.len(), 40); // 20 bytes = 40 hex chars
    }

    #[test]
    fn test_record_validation() {
        let record = BlockchainRecord {
            id: Uuid::new_v4(),
            transaction_type: TransactionType::ScanResult,
            timestamp: chrono::Utc::now(),
            data_hash: "abcdef1234567890".to_string(),
            previous_hash: None,
            signature: "fedcba0987654321".to_string(),
            public_key: "1234567890abcdef".to_string(),
            metadata: HashMap::new(),
            merkle_proof: None,
        };

        assert!(utils::validate_record(&record).is_ok());
    }
}
