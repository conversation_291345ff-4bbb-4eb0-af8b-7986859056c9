//! Database connection management and pooling

use crate::config::DatabaseConfig;
use crate::error::{Result, InfinitiumError};
use sqlx::{PgPool, Pool, Postgres, Row};
use std::time::Duration;
use tracing::{info, warn, error};

/// Database connection pool wrapper
#[derive(Debug, Clone)]
pub struct DatabasePool {
    pool: PgPool,
}

impl DatabasePool {
    /// Create a new database connection pool
    pub async fn new(config: &DatabaseConfig) -> Result<Self> {
        info!("Connecting to database: {}", mask_database_url(&config.url));
        
        let pool = sqlx::postgres::PgPoolOptions::new()
            .max_connections(config.max_connections)
            .min_connections(config.min_connections)
            .acquire_timeout(Duration::from_secs(30))
            .idle_timeout(Duration::from_secs(600))
            .max_lifetime(Duration::from_secs(1800))
            .test_before_acquire(true)
            .connect(&config.url)
            .await
            .map_err(|e| {
                error!("Failed to connect to database: {}", e);
                InfinitiumError::DatabaseError(e.to_string())
            })?;

        info!("Database connection pool created successfully");
        
        Ok(Self { pool })
    }

    /// Get the underlying pool
    pub fn pool(&self) -> &PgPool {
        &self.pool
    }

    /// Test database connectivity
    pub async fn test_connection(&self) -> Result<()> {
        let row = sqlx::query("SELECT 1 as test")
            .fetch_one(&self.pool)
            .await
            .map_err(|e| InfinitiumError::DatabaseError(e.to_string()))?;

        let test_value: i32 = row.try_get("test")
            .map_err(|e| InfinitiumError::DatabaseError(e.to_string()))?;

        if test_value == 1 {
            info!("Database connection test successful");
            Ok(())
        } else {
            Err(InfinitiumError::DatabaseError("Connection test failed".to_string()))
        }
    }

    /// Get database version
    pub async fn get_version(&self) -> Result<String> {
        let row = sqlx::query("SELECT version() as version")
            .fetch_one(&self.pool)
            .await
            .map_err(|e| InfinitiumError::DatabaseError(e.to_string()))?;

        let version: String = row.try_get("version")
            .map_err(|e| InfinitiumError::DatabaseError(e.to_string()))?;

        Ok(version)
    }

    /// Check if database is ready (all tables exist)
    pub async fn is_ready(&self) -> Result<bool> {
        let row = sqlx::query(r#"
            SELECT COUNT(*) as table_count
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN (
                'scan_results', 'sbom_records', 'hbom_records', 
                'vulnerabilities', 'package_vulnerabilities', 
                'compliance_reports', 'blockchain_commits', 
                'verifiable_credentials', 'api_keys', 'user_sessions', 
                'audit_logs', 'system_config'
            )
        "#)
        .fetch_one(&self.pool)
        .await
        .map_err(|e| InfinitiumError::DatabaseError(e.to_string()))?;

        let table_count: i64 = row.try_get("table_count")
            .map_err(|e| InfinitiumError::DatabaseError(e.to_string()))?;

        Ok(table_count == 12) // Expected number of tables
    }

    /// Get connection pool statistics
    pub async fn get_pool_stats(&self) -> PoolStats {
        PoolStats {
            size: self.pool.size(),
            idle: self.pool.num_idle(),
            active: self.pool.size() - self.pool.num_idle(),
        }
    }

    /// Close the connection pool
    pub async fn close(&self) {
        info!("Closing database connection pool");
        self.pool.close().await;
    }

    /// Execute a health check query
    pub async fn health_check(&self) -> Result<HealthStatus> {
        let start = std::time::Instant::now();
        
        match self.test_connection().await {
            Ok(_) => {
                let duration = start.elapsed();
                Ok(HealthStatus {
                    is_healthy: true,
                    response_time_ms: duration.as_millis() as u64,
                    error: None,
                })
            }
            Err(e) => {
                let duration = start.elapsed();
                warn!("Database health check failed: {}", e);
                Ok(HealthStatus {
                    is_healthy: false,
                    response_time_ms: duration.as_millis() as u64,
                    error: Some(e.to_string()),
                })
            }
        }
    }

    /// Begin a database transaction
    pub async fn begin_transaction(&self) -> Result<sqlx::Transaction<'_, Postgres>> {
        self.pool.begin()
            .await
            .map_err(|e| InfinitiumError::DatabaseError(e.to_string()))
    }

    /// Execute a query and return the number of affected rows
    pub async fn execute(&self, query: &str) -> Result<u64> {
        let result = sqlx::query(query)
            .execute(&self.pool)
            .await
            .map_err(|e| InfinitiumError::DatabaseError(e.to_string()))?;

        Ok(result.rows_affected())
    }

    /// Check if a table exists
    pub async fn table_exists(&self, table_name: &str) -> Result<bool> {
        let row = sqlx::query(r#"
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = $1
            ) as exists
        "#)
        .bind(table_name)
        .fetch_one(&self.pool)
        .await
        .map_err(|e| InfinitiumError::DatabaseError(e.to_string()))?;

        let exists: bool = row.try_get("exists")
            .map_err(|e| InfinitiumError::DatabaseError(e.to_string()))?;

        Ok(exists)
    }

    /// Get table row count
    pub async fn get_table_count(&self, table_name: &str) -> Result<i64> {
        // Validate table name to prevent SQL injection
        if !is_valid_table_name(table_name) {
            return Err(InfinitiumError::ValidationError("Invalid table name".to_string()));
        }

        let query = format!("SELECT COUNT(*) as count FROM {}", table_name);
        let row = sqlx::query(&query)
            .fetch_one(&self.pool)
            .await
            .map_err(|e| InfinitiumError::DatabaseError(e.to_string()))?;

        let count: i64 = row.try_get("count")
            .map_err(|e| InfinitiumError::DatabaseError(e.to_string()))?;

        Ok(count)
    }
}

/// Connection pool statistics
#[derive(Debug, Clone)]
pub struct PoolStats {
    pub size: u32,
    pub idle: u32,
    pub active: u32,
}

/// Database health status
#[derive(Debug, Clone)]
pub struct HealthStatus {
    pub is_healthy: bool,
    pub response_time_ms: u64,
    pub error: Option<String>,
}

/// Mask sensitive information in database URL for logging
fn mask_database_url(url: &str) -> String {
    if let Ok(parsed_url) = url::Url::parse(url) {
        let mut masked = parsed_url.clone();
        if masked.password().is_some() {
            let _ = masked.set_password(Some("***"));
        }
        masked.to_string()
    } else {
        "***".to_string()
    }
}

/// Validate table name to prevent SQL injection
fn is_valid_table_name(table_name: &str) -> bool {
    // Only allow alphanumeric characters and underscores
    table_name.chars().all(|c| c.is_alphanumeric() || c == '_') 
        && !table_name.is_empty()
        && table_name.len() <= 63 // PostgreSQL identifier limit
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_mask_database_url() {
        let url = "postgres://user:password@localhost:5432/db";
        let masked = mask_database_url(url);
        assert!(masked.contains("***"));
        assert!(!masked.contains("password"));
    }

    #[test]
    fn test_is_valid_table_name() {
        assert!(is_valid_table_name("scan_results"));
        assert!(is_valid_table_name("table123"));
        assert!(!is_valid_table_name("table; DROP TABLE users;"));
        assert!(!is_valid_table_name("table with spaces"));
        assert!(!is_valid_table_name(""));
    }
}
