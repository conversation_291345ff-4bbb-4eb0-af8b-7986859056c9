//! Database migration management

use crate::database::{connection::DatabasePool, schema::{CREATE_TABLES_SQL, DROP_TABLES_SQL}};
use crate::error::{Result, InfinitiumError};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use sqlx::{Row, FromRow};
use tracing::{info, warn, error};
use uuid::Uuid;

/// Migration record
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Migration {
    pub id: Uuid,
    pub version: String,
    pub name: String,
    pub sql: String,
    pub checksum: String,
    pub applied_at: DateTime<Utc>,
}

/// Migration manager
pub struct MigrationManager {
    pool: DatabasePool,
}

impl MigrationManager {
    /// Create a new migration manager
    pub fn new(pool: DatabasePool) -> Self {
        Self { pool }
    }

    /// Initialize the migration system
    pub async fn initialize(&self) -> Result<()> {
        info!("Initializing migration system");
        
        // Create migrations table if it doesn't exist
        let create_migrations_table = r#"
            CREATE TABLE IF NOT EXISTS migrations (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                version VARCHAR(50) UNIQUE NOT NULL,
                name VARCHAR(255) NOT NULL,
                sql TEXT NOT NULL,
                checksum VARCHAR(64) NOT NULL,
                applied_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
            );
            
            CREATE INDEX IF NOT EXISTS idx_migrations_version ON migrations(version);
            CREATE INDEX IF NOT EXISTS idx_migrations_applied_at ON migrations(applied_at);
        "#;

        self.pool.execute(create_migrations_table).await?;
        info!("Migration system initialized");
        
        Ok(())
    }

    /// Run all pending migrations
    pub async fn migrate(&self) -> Result<Vec<Migration>> {
        info!("Running database migrations");
        
        let mut applied_migrations = Vec::new();
        let pending_migrations = self.get_pending_migrations().await?;
        
        if pending_migrations.is_empty() {
            info!("No pending migrations");
            return Ok(applied_migrations);
        }

        for migration in pending_migrations {
            info!("Applying migration: {} - {}", migration.version, migration.name);
            
            let mut tx = self.pool.begin_transaction().await?;
            
            // Execute the migration SQL
            match sqlx::query(&migration.sql).execute(&mut *tx).await {
                Ok(_) => {
                    // Record the migration
                    sqlx::query(r#"
                        INSERT INTO migrations (version, name, sql, checksum, applied_at)
                        VALUES ($1, $2, $3, $4, $5)
                    "#)
                    .bind(&migration.version)
                    .bind(&migration.name)
                    .bind(&migration.sql)
                    .bind(&migration.checksum)
                    .bind(migration.applied_at)
                    .execute(&mut *tx)
                    .await
                    .map_err(|e| InfinitiumError::DatabaseError(e.to_string()))?;

                    tx.commit().await.map_err(|e| InfinitiumError::DatabaseError(e.to_string()))?;
                    
                    info!("Migration applied successfully: {}", migration.version);
                    applied_migrations.push(migration);
                }
                Err(e) => {
                    error!("Migration failed: {} - {}", migration.version, e);
                    tx.rollback().await.map_err(|e| InfinitiumError::DatabaseError(e.to_string()))?;
                    return Err(InfinitiumError::DatabaseError(format!("Migration failed: {}", e)));
                }
            }
        }

        info!("All migrations applied successfully");
        Ok(applied_migrations)
    }

    /// Get all applied migrations
    pub async fn get_applied_migrations(&self) -> Result<Vec<Migration>> {
        let migrations = sqlx::query_as::<_, Migration>(r#"
            SELECT id, version, name, sql, checksum, applied_at
            FROM migrations
            ORDER BY applied_at ASC
        "#)
        .fetch_all(self.pool.pool())
        .await
        .map_err(|e| InfinitiumError::DatabaseError(e.to_string()))?;

        Ok(migrations)
    }

    /// Get pending migrations
    async fn get_pending_migrations(&self) -> Result<Vec<Migration>> {
        let applied_versions = self.get_applied_migration_versions().await?;
        let all_migrations = self.get_all_migrations();
        
        let pending: Vec<Migration> = all_migrations
            .into_iter()
            .filter(|m| !applied_versions.contains(&m.version))
            .collect();

        Ok(pending)
    }

    /// Get applied migration versions
    async fn get_applied_migration_versions(&self) -> Result<Vec<String>> {
        let rows = sqlx::query("SELECT version FROM migrations ORDER BY applied_at ASC")
            .fetch_all(self.pool.pool())
            .await
            .map_err(|e| InfinitiumError::DatabaseError(e.to_string()))?;

        let versions: Vec<String> = rows
            .into_iter()
            .map(|row| row.try_get::<String, _>("version").unwrap_or_default())
            .collect();

        Ok(versions)
    }

    /// Get all available migrations
    fn get_all_migrations(&self) -> Vec<Migration> {
        vec![
            Migration {
                id: Uuid::new_v4(),
                version: "001".to_string(),
                name: "Initial schema".to_string(),
                sql: CREATE_TABLES_SQL.to_string(),
                checksum: calculate_checksum(CREATE_TABLES_SQL),
                applied_at: Utc::now(),
            },
            // Add more migrations here as needed
        ]
    }

    /// Rollback the last migration
    pub async fn rollback_last(&self) -> Result<Option<Migration>> {
        warn!("Rolling back last migration");
        
        let last_migration = self.get_last_migration().await?;
        
        if let Some(migration) = last_migration {
            let mut tx = self.pool.begin_transaction().await?;
            
            // Note: This is a simplified rollback that drops all tables
            // In a real system, you'd want proper down migrations
            match sqlx::query(DROP_TABLES_SQL).execute(&mut *tx).await {
                Ok(_) => {
                    // Remove the migration record
                    sqlx::query("DELETE FROM migrations WHERE version = $1")
                        .bind(&migration.version)
                        .execute(&mut *tx)
                        .await
                        .map_err(|e| InfinitiumError::DatabaseError(e.to_string()))?;

                    tx.commit().await.map_err(|e| InfinitiumError::DatabaseError(e.to_string()))?;
                    
                    warn!("Migration rolled back: {}", migration.version);
                    Ok(Some(migration))
                }
                Err(e) => {
                    error!("Rollback failed: {}", e);
                    tx.rollback().await.map_err(|e| InfinitiumError::DatabaseError(e.to_string()))?;
                    Err(InfinitiumError::DatabaseError(format!("Rollback failed: {}", e)))
                }
            }
        } else {
            info!("No migrations to rollback");
            Ok(None)
        }
    }

    /// Get the last applied migration
    async fn get_last_migration(&self) -> Result<Option<Migration>> {
        let migration = sqlx::query_as::<_, Migration>(r#"
            SELECT id, version, name, sql, checksum, applied_at
            FROM migrations
            ORDER BY applied_at DESC
            LIMIT 1
        "#)
        .fetch_optional(self.pool.pool())
        .await
        .map_err(|e| InfinitiumError::DatabaseError(e.to_string()))?;

        Ok(migration)
    }

    /// Reset the database (drop all tables and migrations)
    pub async fn reset(&self) -> Result<()> {
        warn!("Resetting database - this will drop all data!");
        
        let mut tx = self.pool.begin_transaction().await?;
        
        // Drop all tables
        match sqlx::query(DROP_TABLES_SQL).execute(&mut *tx).await {
            Ok(_) => {
                // Drop migrations table
                sqlx::query("DROP TABLE IF EXISTS migrations CASCADE")
                    .execute(&mut *tx)
                    .await
                    .map_err(|e| InfinitiumError::DatabaseError(e.to_string()))?;

                tx.commit().await.map_err(|e| InfinitiumError::DatabaseError(e.to_string()))?;
                
                warn!("Database reset completed");
                Ok(())
            }
            Err(e) => {
                error!("Database reset failed: {}", e);
                tx.rollback().await.map_err(|e| InfinitiumError::DatabaseError(e.to_string()))?;
                Err(InfinitiumError::DatabaseError(format!("Reset failed: {}", e)))
            }
        }
    }

    /// Check migration status
    pub async fn status(&self) -> Result<MigrationStatus> {
        let applied = self.get_applied_migrations().await?;
        let pending = self.get_pending_migrations().await?;
        
        Ok(MigrationStatus {
            applied_count: applied.len(),
            pending_count: pending.len(),
            last_migration: applied.last().map(|m| m.version.clone()),
            is_up_to_date: pending.is_empty(),
        })
    }
}

/// Migration status information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MigrationStatus {
    pub applied_count: usize,
    pub pending_count: usize,
    pub last_migration: Option<String>,
    pub is_up_to_date: bool,
}

/// Calculate checksum for migration content
fn calculate_checksum(content: &str) -> String {
    use sha2::{Digest, Sha256};
    let mut hasher = Sha256::new();
    hasher.update(content.as_bytes());
    hex::encode(hasher.finalize())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_calculate_checksum() {
        let content = "CREATE TABLE test (id INT);";
        let checksum1 = calculate_checksum(content);
        let checksum2 = calculate_checksum(content);
        
        assert_eq!(checksum1, checksum2);
        assert_eq!(checksum1.len(), 64); // SHA-256 hex length
    }

    #[test]
    fn test_migration_status() {
        let status = MigrationStatus {
            applied_count: 5,
            pending_count: 0,
            last_migration: Some("005".to_string()),
            is_up_to_date: true,
        };
        
        assert!(status.is_up_to_date);
        assert_eq!(status.applied_count, 5);
    }
}
