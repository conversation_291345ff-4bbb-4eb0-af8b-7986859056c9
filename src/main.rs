use anyhow::Result;
use clap::Parser;
use infinitum_signal::{
    api::server::ApiServer,
    config::Config,
    error::InfinitumError,
    logging::setup_logging,
    metrics::setup_metrics,
};
use std::sync::Arc;
use tokio::signal;
use tracing::{error, info, warn};

#[derive(Parser)]
#[command(name = "infinitum-signal")]
#[command(about = "Enterprise Cyber-Compliance Platform")]
#[command(version = env!("CARGO_PKG_VERSION"))]
struct Cli {
    /// Configuration file path
    #[arg(short, long, default_value = "config.yaml")]
    config: String,

    /// Server host
    #[arg(long, env = "SERVER_HOST")]
    host: Option<String>,

    /// Server port
    #[arg(long, env = "SERVER_PORT")]
    port: Option<u16>,

    /// Log level
    #[arg(long, env = "RUST_LOG")]
    log_level: Option<String>,

    /// Enable development mode
    #[arg(long, env = "DEV_MODE")]
    dev_mode: bool,
}

#[tokio::main]
async fn main() -> Result<()> {
    // Parse command line arguments
    let cli = Cli::parse();

    // Load configuration
    let config = Config::load(&cli.config).await?;

    // Setup logging
    setup_logging(&config.logging)?;

    info!(
        "Starting Infinitium Signal v{}",
        env!("CARGO_PKG_VERSION")
    );

    // Override config with CLI arguments
    let mut config = config;
    if let Some(host) = cli.host {
        config.server.host = host;
    }
    if let Some(port) = cli.port {
        config.server.port = port;
    }

    // Setup metrics
    setup_metrics(&config.metrics)?;

    // Create shared config
    let config = Arc::new(config);

    // Start the API server
    let server = ApiServer::new(config.clone()).await?;
    let server_handle = tokio::spawn(async move {
        if let Err(e) = server.run().await {
            error!("Server error: {}", e);
        }
    });

    // Setup graceful shutdown
    let shutdown_signal = async {
        signal::ctrl_c()
            .await
            .expect("Failed to install CTRL+C signal handler");
        warn!("Received shutdown signal");
    };

    // Wait for shutdown signal or server error
    tokio::select! {
        _ = shutdown_signal => {
            info!("Shutting down gracefully...");
        }
        _ = server_handle => {
            error!("Server stopped unexpectedly");
        }
    }

    info!("Infinitium Signal shutdown complete");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cli_parsing() {
        let cli = Cli::try_parse_from(&[
            "infinitum-signal",
            "--config",
            "test.yaml",
            "--host",
            "127.0.0.1",
            "--port",
            "3000",
        ]);

        assert!(cli.is_ok());
        let cli = cli.unwrap();
        assert_eq!(cli.config, "test.yaml");
        assert_eq!(cli.host, Some("127.0.0.1".to_string()));
        assert_eq!(cli.port, Some(3000));
    }
}
