use crate::{config::LoggingConfig, error::Result};
use anyhow::Context;
use std::str::FromStr;
use tracing::{info, Level};
use tracing_appender::{non_blocking, rolling};
use tracing_subscriber::{
    fmt::{self, format::FmtSpan},
    layer::SubscriberExt,
    util::SubscriberInitExt,
    EnvFilter, Layer,
};

/// Setup logging based on configuration
pub fn setup_logging(config: &LoggingConfig) -> Result<()> {
    let level = Level::from_str(&config.level)
        .with_context(|| format!("Invalid log level: {}", config.level))?;

    // Create environment filter
    let env_filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new(format!("{}={}", env!("CARGO_PKG_NAME"), level)));

    // Create base subscriber
    let subscriber = tracing_subscriber::registry().with(env_filter);

    // Setup console logging
    let console_layer = create_console_layer(config)?;

    // Setup file logging if configured
    if let Some(file_path) = &config.file {
        let file_layer = create_file_layer(config, file_path)?;
        subscriber.with(console_layer).with(file_layer).init();
    } else {
        subscriber.with(console_layer).init();
    }

    info!(
        level = %level,
        format = %config.format,
        file = ?config.file,
        "Logging initialized"
    );

    Ok(())
}

/// Create console logging layer
fn create_console_layer(
    config: &LoggingConfig,
) -> Result<Box<dyn Layer<tracing_subscriber::Registry> + Send + Sync>> {
    let layer = match config.format.as_str() {
        "json" => fmt::layer()
            .json()
            .with_current_span(true)
            .with_span_events(FmtSpan::CLOSE)
            .with_target(true)
            .with_thread_ids(true)
            .with_thread_names(true)
            .boxed(),
        "pretty" => fmt::layer()
            .pretty()
            .with_current_span(true)
            .with_span_events(FmtSpan::CLOSE)
            .with_target(true)
            .with_thread_ids(true)
            .with_thread_names(true)
            .boxed(),
        "compact" => fmt::layer()
            .compact()
            .with_current_span(true)
            .with_span_events(FmtSpan::CLOSE)
            .with_target(true)
            .boxed(),
        _ => fmt::layer()
            .with_current_span(true)
            .with_span_events(FmtSpan::CLOSE)
            .with_target(true)
            .boxed(),
    };

    Ok(layer)
}

/// Create file logging layer
fn create_file_layer(
    config: &LoggingConfig,
    file_path: &str,
) -> Result<Box<dyn Layer<tracing_subscriber::Registry> + Send + Sync>> {
    // Parse file path to get directory and filename
    let path = std::path::Path::new(file_path);
    let directory = path
        .parent()
        .unwrap_or_else(|| std::path::Path::new("."));
    let filename = path
        .file_name()
        .and_then(|name| name.to_str())
        .unwrap_or("infinitum-signal.log");

    // Create rolling file appender
    let file_appender = rolling::daily(directory, filename);
    let (non_blocking, _guard) = non_blocking(file_appender);

    // Create file layer based on format
    let layer = match config.format.as_str() {
        "json" => fmt::layer()
            .json()
            .with_writer(non_blocking)
            .with_current_span(true)
            .with_span_events(FmtSpan::CLOSE)
            .with_target(true)
            .with_thread_ids(true)
            .with_thread_names(true)
            .with_ansi(false)
            .boxed(),
        _ => fmt::layer()
            .with_writer(non_blocking)
            .with_current_span(true)
            .with_span_events(FmtSpan::CLOSE)
            .with_target(true)
            .with_ansi(false)
            .boxed(),
    };

    Ok(layer)
}

/// Logging middleware for HTTP requests
pub mod middleware {
    use axum::{
        extract::MatchedPath,
        http::{Request, Response},
        middleware::Next,
        response::IntoResponse,
    };
    use std::time::Instant;
    use tracing::{info_span, Instrument};
    use uuid::Uuid;

    /// HTTP request logging middleware
    pub async fn logging_middleware<B>(
        request: Request<B>,
        next: Next<B>,
    ) -> impl IntoResponse {
        let start = Instant::now();
        let request_id = Uuid::new_v4().to_string();
        
        let method = request.method().clone();
        let uri = request.uri().clone();
        let version = request.version();
        let headers = request.headers().clone();
        
        // Get matched path for better grouping
        let path = request
            .extensions()
            .get::<MatchedPath>()
            .map(|matched_path| matched_path.as_str())
            .unwrap_or_else(|| uri.path());

        // Create span for the request
        let span = info_span!(
            "http_request",
            method = %method,
            path = %path,
            version = ?version,
            request_id = %request_id,
        );

        async move {
            let response = next.run(request).await;
            let duration = start.elapsed();
            let status = response.status();

            tracing::info!(
                status = %status,
                duration_ms = %duration.as_millis(),
                user_agent = ?headers.get("user-agent"),
                "HTTP request completed"
            );

            response
        }
        .instrument(span)
        .await
    }
}

/// Structured logging macros
#[macro_export]
macro_rules! log_scan_start {
    ($scan_id:expr, $project_path:expr, $scan_type:expr) => {
        tracing::info!(
            scan_id = %$scan_id,
            project_path = %$project_path,
            scan_type = %$scan_type,
            "Scan started"
        );
    };
}

#[macro_export]
macro_rules! log_scan_complete {
    ($scan_id:expr, $duration:expr, $dependencies:expr) => {
        tracing::info!(
            scan_id = %$scan_id,
            duration_ms = %$duration.as_millis(),
            dependencies_found = %$dependencies,
            "Scan completed successfully"
        );
    };
}

#[macro_export]
macro_rules! log_scan_error {
    ($scan_id:expr, $error:expr) => {
        tracing::error!(
            scan_id = %$scan_id,
            error = %$error,
            "Scan failed"
        );
    };
}

#[macro_export]
macro_rules! log_vulnerability_found {
    ($cve_id:expr, $severity:expr, $package:expr) => {
        tracing::warn!(
            cve_id = %$cve_id,
            severity = %$severity,
            package = %$package,
            "Vulnerability found"
        );
    };
}

#[macro_export]
macro_rules! log_compliance_report {
    ($framework:expr, $status:expr, $issues:expr) => {
        tracing::info!(
            framework = %$framework,
            status = %$status,
            issues_found = %$issues,
            "Compliance report generated"
        );
    };
}

#[macro_export]
macro_rules! log_blockchain_transaction {
    ($tx_id:expr, $operation:expr) => {
        tracing::info!(
            tx_id = %$tx_id,
            operation = %$operation,
            "Blockchain transaction recorded"
        );
    };
}

/// Performance monitoring utilities
pub mod performance {
    use std::time::Instant;
    use tracing::{info, warn};

    /// Timer for measuring operation duration
    pub struct Timer {
        start: Instant,
        operation: String,
        warn_threshold_ms: Option<u64>,
    }

    impl Timer {
        pub fn new(operation: impl Into<String>) -> Self {
            Self {
                start: Instant::now(),
                operation: operation.into(),
                warn_threshold_ms: None,
            }
        }

        pub fn with_warn_threshold(mut self, threshold_ms: u64) -> Self {
            self.warn_threshold_ms = Some(threshold_ms);
            self
        }

        pub fn elapsed(&self) -> std::time::Duration {
            self.start.elapsed()
        }
    }

    impl Drop for Timer {
        fn drop(&mut self) {
            let duration = self.elapsed();
            let duration_ms = duration.as_millis();

            if let Some(threshold) = self.warn_threshold_ms {
                if duration_ms > threshold as u128 {
                    warn!(
                        operation = %self.operation,
                        duration_ms = %duration_ms,
                        threshold_ms = %threshold,
                        "Operation exceeded performance threshold"
                    );
                    return;
                }
            }

            info!(
                operation = %self.operation,
                duration_ms = %duration_ms,
                "Operation completed"
            );
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_logging_config_validation() {
        let config = LoggingConfig {
            level: "info".to_string(),
            format: "json".to_string(),
            file: None,
            max_size: "100MB".to_string(),
            max_files: 10,
            compress: true,
        };

        // Test that we can parse the log level
        assert!(Level::from_str(&config.level).is_ok());
    }

    #[test]
    fn test_invalid_log_level() {
        let config = LoggingConfig {
            level: "invalid".to_string(),
            format: "json".to_string(),
            file: None,
            max_size: "100MB".to_string(),
            max_files: 10,
            compress: true,
        };

        assert!(Level::from_str(&config.level).is_err());
    }
}
