use crate::{
    config::ScanningConfig,
    error::{InfinitumError, Result},
    metrics::ScanMetrics,
};
use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    path::{Path, PathBuf},
    process::Command,
};
use tokio::{fs, process::Command as AsyncCommand};
use tracing::{debug, info, instrument, warn};

/// Hardware Bill of Materials scanner
pub struct HbomScanner {
    config: ScanningConfig,
}

/// Hardware component information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HardwareComponent {
    /// Component name/identifier
    pub name: String,
    /// Component type (e.g., "microcontroller", "sensor", "memory")
    pub component_type: String,
    /// Manufacturer
    pub manufacturer: Option<String>,
    /// Model number
    pub model: Option<String>,
    /// Version/revision
    pub version: Option<String>,
    /// Part number
    pub part_number: Option<String>,
    /// Component description
    pub description: Option<String>,
    /// Firmware version
    pub firmware_version: Option<String>,
    /// Hardware specifications
    pub specifications: HashMap<String, String>,
    /// File path where component was detected
    pub file_path: PathBuf,
    /// Component hash/checksum
    pub hash: Option<String>,
    /// Security features
    pub security_features: Vec<String>,
    /// Compliance certifications
    pub certifications: Vec<String>,
    /// Additional metadata
    pub metadata: HashMap<String, String>,
}

/// Firmware analysis result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FirmwareAnalysis {
    /// Firmware file path
    pub file_path: PathBuf,
    /// Detected architecture
    pub architecture: Option<String>,
    /// Endianness
    pub endianness: Option<String>,
    /// Entry point address
    pub entry_point: Option<String>,
    /// Detected strings
    pub strings: Vec<String>,
    /// Embedded certificates
    pub certificates: Vec<String>,
    /// Cryptographic keys found
    pub crypto_keys: Vec<String>,
    /// File system information
    pub filesystem: Option<FilesystemInfo>,
    /// Security analysis
    pub security_analysis: SecurityAnalysis,
}

/// Filesystem information from firmware
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FilesystemInfo {
    /// Filesystem type
    pub fs_type: String,
    /// Files found
    pub files: Vec<String>,
    /// Configuration files
    pub config_files: Vec<String>,
    /// Executable files
    pub executables: Vec<String>,
}

/// Security analysis of firmware
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityAnalysis {
    /// Encryption detected
    pub encryption_detected: bool,
    /// Hardcoded credentials found
    pub hardcoded_credentials: Vec<String>,
    /// Weak cryptography detected
    pub weak_crypto: Vec<String>,
    /// Debug interfaces enabled
    pub debug_interfaces: Vec<String>,
    /// Security vulnerabilities
    pub vulnerabilities: Vec<String>,
}

/// Binary file type detection
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum BinaryType {
    /// ELF executable
    Elf,
    /// PE executable
    Pe,
    /// Raw binary
    Raw,
    /// Intel HEX format
    IntelHex,
    /// Motorola S-record
    SRecord,
    /// Unknown format
    Unknown,
}

impl HbomScanner {
    /// Create new HBOM scanner
    pub fn new(config: &ScanningConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }

    /// Scan for hardware components
    #[instrument(skip(self), fields(target_path = %target_path.display()))]
    pub async fn scan_hardware(&self, target_path: &Path) -> Result<Vec<HardwareComponent>> {
        info!("Starting HBOM scan");
        ScanMetrics::scan_started("hbom");

        let start_time = std::time::Instant::now();
        let mut components = Vec::new();

        // Scan for firmware files
        let firmware_files = self.find_firmware_files(target_path).await?;
        debug!("Found {} firmware files", firmware_files.len());

        for firmware_file in firmware_files {
            match self.analyze_firmware(&firmware_file).await {
                Ok(analysis) => {
                    let hardware_components = self.extract_hardware_info(&analysis).await?;
                    components.extend(hardware_components);
                }
                Err(e) => {
                    warn!("Failed to analyze firmware {}: {}", firmware_file.display(), e);
                }
            }
        }

        // Scan for hardware description files
        let hardware_desc_files = self.find_hardware_description_files(target_path).await?;
        for desc_file in hardware_desc_files {
            match self.parse_hardware_description(&desc_file).await {
                Ok(hardware_components) => {
                    components.extend(hardware_components);
                }
                Err(e) => {
                    warn!("Failed to parse hardware description {}: {}", desc_file.display(), e);
                }
            }
        }

        let duration = start_time.elapsed();
        ScanMetrics::scan_completed("hbom", duration);

        info!(
            components_found = components.len(),
            duration_ms = duration.as_millis(),
            "HBOM scan completed"
        );

        Ok(components)
    }

    /// Find firmware files in the target directory
    async fn find_firmware_files(&self, target_path: &Path) -> Result<Vec<PathBuf>> {
        let mut firmware_files = Vec::new();

        // Common firmware file extensions
        let firmware_extensions = [
            "bin", "hex", "elf", "img", "rom", "fw", "firmware",
            "s19", "s28", "s37", "srec", "mot", "ihex",
        ];

        let mut entries = fs::read_dir(target_path).await?;
        while let Some(entry) = entries.next_entry().await? {
            let path = entry.path();
            
            if path.is_file() {
                if let Some(extension) = path.extension().and_then(|ext| ext.to_str()) {
                    if firmware_extensions.contains(&extension.to_lowercase().as_str()) {
                        firmware_files.push(path);
                    }
                }
                
                // Also check for files with firmware-related names
                if let Some(file_name) = path.file_name().and_then(|name| name.to_str()) {
                    let file_name_lower = file_name.to_lowercase();
                    if file_name_lower.contains("firmware") 
                        || file_name_lower.contains("bootloader")
                        || file_name_lower.contains("bios") {
                        firmware_files.push(path);
                    }
                }
            }
        }

        Ok(firmware_files)
    }

    /// Analyze firmware file
    async fn analyze_firmware(&self, firmware_path: &Path) -> Result<FirmwareAnalysis> {
        debug!("Analyzing firmware: {}", firmware_path.display());

        let binary_type = self.detect_binary_type(firmware_path).await?;
        let mut analysis = FirmwareAnalysis {
            file_path: firmware_path.to_path_buf(),
            architecture: None,
            endianness: None,
            entry_point: None,
            strings: Vec::new(),
            certificates: Vec::new(),
            crypto_keys: Vec::new(),
            filesystem: None,
            security_analysis: SecurityAnalysis {
                encryption_detected: false,
                hardcoded_credentials: Vec::new(),
                weak_crypto: Vec::new(),
                debug_interfaces: Vec::new(),
                vulnerabilities: Vec::new(),
            },
        };

        // Analyze based on binary type
        match binary_type {
            BinaryType::Elf => {
                self.analyze_elf_file(firmware_path, &mut analysis).await?;
            }
            BinaryType::Pe => {
                self.analyze_pe_file(firmware_path, &mut analysis).await?;
            }
            BinaryType::Raw | BinaryType::IntelHex | BinaryType::SRecord => {
                self.analyze_raw_binary(firmware_path, &mut analysis).await?;
            }
            BinaryType::Unknown => {
                warn!("Unknown binary type for {}", firmware_path.display());
                self.analyze_raw_binary(firmware_path, &mut analysis).await?;
            }
        }

        // Extract strings
        analysis.strings = self.extract_strings(firmware_path).await?;

        // Perform security analysis
        self.perform_security_analysis(&mut analysis).await?;

        Ok(analysis)
    }

    /// Detect binary file type
    async fn detect_binary_type(&self, file_path: &Path) -> Result<BinaryType> {
        let mut file_content = vec![0u8; 16];
        let bytes_read = fs::File::open(file_path).await?
            .try_read(&mut file_content)?;

        if bytes_read >= 4 {
            // Check ELF magic
            if file_content[0..4] == [0x7f, 0x45, 0x4c, 0x46] {
                return Ok(BinaryType::Elf);
            }

            // Check PE magic
            if file_content[0..2] == [0x4d, 0x5a] {
                return Ok(BinaryType::Pe);
            }

            // Check Intel HEX
            if file_content[0] == b':' {
                return Ok(BinaryType::IntelHex);
            }

            // Check Motorola S-record
            if file_content[0] == b'S' && file_content[1].is_ascii_digit() {
                return Ok(BinaryType::SRecord);
            }
        }

        Ok(BinaryType::Raw)
    }

    /// Analyze ELF file
    async fn analyze_elf_file(&self, file_path: &Path, analysis: &mut FirmwareAnalysis) -> Result<()> {
        // Use readelf command if available
        if let Ok(output) = AsyncCommand::new("readelf")
            .args(["-h", file_path.to_str().unwrap()])
            .output()
            .await
        {
            let output_str = String::from_utf8_lossy(&output.stdout);
            
            // Parse architecture
            if let Some(machine_line) = output_str.lines().find(|line| line.contains("Machine:")) {
                analysis.architecture = machine_line.split(':').nth(1).map(|s| s.trim().to_string());
            }

            // Parse entry point
            if let Some(entry_line) = output_str.lines().find(|line| line.contains("Entry point address:")) {
                analysis.entry_point = entry_line.split(':').nth(1).map(|s| s.trim().to_string());
            }
        }

        Ok(())
    }

    /// Analyze PE file
    async fn analyze_pe_file(&self, _file_path: &Path, _analysis: &mut FirmwareAnalysis) -> Result<()> {
        // TODO: Implement PE file analysis
        Ok(())
    }

    /// Analyze raw binary
    async fn analyze_raw_binary(&self, _file_path: &Path, _analysis: &mut FirmwareAnalysis) -> Result<()> {
        // TODO: Implement raw binary analysis
        Ok(())
    }

    /// Extract strings from firmware
    async fn extract_strings(&self, file_path: &Path) -> Result<Vec<String>> {
        let mut strings = Vec::new();

        // Use strings command if available
        if let Ok(output) = AsyncCommand::new("strings")
            .args(["-n", "4", file_path.to_str().unwrap()])
            .output()
            .await
        {
            let output_str = String::from_utf8_lossy(&output.stdout);
            strings = output_str.lines().map(|s| s.to_string()).collect();
        }

        Ok(strings)
    }

    /// Perform security analysis
    async fn perform_security_analysis(&self, analysis: &mut FirmwareAnalysis) -> Result<()> {
        // Check for hardcoded credentials in strings
        for string in &analysis.strings {
            let string_lower = string.to_lowercase();
            
            // Look for common credential patterns
            if string_lower.contains("password") 
                || string_lower.contains("passwd")
                || string_lower.contains("secret")
                || string_lower.contains("key=")
                || string_lower.contains("token=") {
                analysis.security_analysis.hardcoded_credentials.push(string.clone());
            }

            // Look for debug interfaces
            if string_lower.contains("jtag")
                || string_lower.contains("debug")
                || string_lower.contains("uart")
                || string_lower.contains("console") {
                analysis.security_analysis.debug_interfaces.push(string.clone());
            }
        }

        // Check for encryption indicators
        for string in &analysis.strings {
            if string.contains("AES") || string.contains("RSA") || string.contains("encrypt") {
                analysis.security_analysis.encryption_detected = true;
                break;
            }
        }

        Ok(())
    }

    /// Extract hardware information from firmware analysis
    async fn extract_hardware_info(&self, analysis: &FirmwareAnalysis) -> Result<Vec<HardwareComponent>> {
        let mut components = Vec::new();

        // Create a component based on the firmware analysis
        let mut component = HardwareComponent {
            name: analysis.file_path.file_name()
                .and_then(|name| name.to_str())
                .unwrap_or("unknown")
                .to_string(),
            component_type: "firmware".to_string(),
            manufacturer: None,
            model: None,
            version: None,
            part_number: None,
            description: Some("Firmware component".to_string()),
            firmware_version: None,
            specifications: HashMap::new(),
            file_path: analysis.file_path.clone(),
            hash: None,
            security_features: Vec::new(),
            certifications: Vec::new(),
            metadata: HashMap::new(),
        };

        // Add architecture information
        if let Some(arch) = &analysis.architecture {
            component.specifications.insert("architecture".to_string(), arch.clone());
        }

        // Add security features
        if analysis.security_analysis.encryption_detected {
            component.security_features.push("encryption".to_string());
        }

        components.push(component);

        Ok(components)
    }

    /// Find hardware description files
    async fn find_hardware_description_files(&self, target_path: &Path) -> Result<Vec<PathBuf>> {
        let mut desc_files = Vec::new();

        // Common hardware description file extensions
        let desc_extensions = ["json", "yaml", "yml", "xml", "toml"];

        let mut entries = fs::read_dir(target_path).await?;
        while let Some(entry) = entries.next_entry().await? {
            let path = entry.path();
            
            if path.is_file() {
                if let Some(extension) = path.extension().and_then(|ext| ext.to_str()) {
                    if desc_extensions.contains(&extension.to_lowercase().as_str()) {
                        // Check if file contains hardware-related content
                        if let Ok(content) = fs::read_to_string(&path).await {
                            if content.to_lowercase().contains("hardware") 
                                || content.to_lowercase().contains("component")
                                || content.to_lowercase().contains("device") {
                                desc_files.push(path);
                            }
                        }
                    }
                }
            }
        }

        Ok(desc_files)
    }

    /// Parse hardware description file
    async fn parse_hardware_description(&self, desc_path: &Path) -> Result<Vec<HardwareComponent>> {
        let content = fs::read_to_string(desc_path).await?;
        let mut components = Vec::new();

        // Try to parse as JSON first
        if let Ok(json_value) = serde_json::from_str::<serde_json::Value>(&content) {
            components.extend(self.extract_components_from_json(&json_value, desc_path)?);
        }
        // Try YAML
        else if let Ok(yaml_value) = serde_yaml::from_str::<serde_yaml::Value>(&content) {
            components.extend(self.extract_components_from_yaml(&yaml_value, desc_path)?);
        }

        Ok(components)
    }

    /// Extract components from JSON
    fn extract_components_from_json(&self, json: &serde_json::Value, file_path: &Path) -> Result<Vec<HardwareComponent>> {
        let mut components = Vec::new();

        // Look for hardware components in various JSON structures
        if let Some(hardware_array) = json.get("hardware").and_then(|h| h.as_array()) {
            for item in hardware_array {
                if let Some(component) = self.parse_json_component(item, file_path)? {
                    components.push(component);
                }
            }
        }

        if let Some(components_array) = json.get("components").and_then(|c| c.as_array()) {
            for item in components_array {
                if let Some(component) = self.parse_json_component(item, file_path)? {
                    components.push(component);
                }
            }
        }

        Ok(components)
    }

    /// Parse JSON component
    fn parse_json_component(&self, json: &serde_json::Value, file_path: &Path) -> Result<Option<HardwareComponent>> {
        if let Some(obj) = json.as_object() {
            let name = obj.get("name")
                .and_then(|n| n.as_str())
                .unwrap_or("unknown")
                .to_string();

            let component = HardwareComponent {
                name,
                component_type: obj.get("type")
                    .and_then(|t| t.as_str())
                    .unwrap_or("unknown")
                    .to_string(),
                manufacturer: obj.get("manufacturer").and_then(|m| m.as_str()).map(|s| s.to_string()),
                model: obj.get("model").and_then(|m| m.as_str()).map(|s| s.to_string()),
                version: obj.get("version").and_then(|v| v.as_str()).map(|s| s.to_string()),
                part_number: obj.get("part_number").and_then(|p| p.as_str()).map(|s| s.to_string()),
                description: obj.get("description").and_then(|d| d.as_str()).map(|s| s.to_string()),
                firmware_version: obj.get("firmware_version").and_then(|f| f.as_str()).map(|s| s.to_string()),
                specifications: HashMap::new(),
                file_path: file_path.to_path_buf(),
                hash: None,
                security_features: Vec::new(),
                certifications: Vec::new(),
                metadata: HashMap::new(),
            };

            return Ok(Some(component));
        }

        Ok(None)
    }

    /// Extract components from YAML
    fn extract_components_from_yaml(&self, _yaml: &serde_yaml::Value, _file_path: &Path) -> Result<Vec<HardwareComponent>> {
        // TODO: Implement YAML parsing similar to JSON
        Ok(Vec::new())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_detect_binary_type() {
        let temp_dir = TempDir::new().unwrap();
        let elf_file = temp_dir.path().join("test.elf");

        // Create a mock ELF file
        fs::write(&elf_file, &[0x7f, 0x45, 0x4c, 0x46]).await.unwrap();

        let config = ScanningConfig::default();
        let scanner = HbomScanner::new(&config);
        let binary_type = scanner.detect_binary_type(&elf_file).await.unwrap();

        assert_eq!(binary_type, BinaryType::Elf);
    }

    #[tokio::test]
    async fn test_find_firmware_files() {
        let temp_dir = TempDir::new().unwrap();
        let project_path = temp_dir.path();

        // Create some firmware files
        fs::write(project_path.join("firmware.bin"), b"test").await.unwrap();
        fs::write(project_path.join("bootloader.hex"), b"test").await.unwrap();
        fs::write(project_path.join("regular.txt"), b"test").await.unwrap();

        let config = ScanningConfig::default();
        let scanner = HbomScanner::new(&config);
        let firmware_files = scanner.find_firmware_files(project_path).await.unwrap();

        assert_eq!(firmware_files.len(), 2);
        assert!(firmware_files.iter().any(|f| f.file_name().unwrap() == "firmware.bin"));
        assert!(firmware_files.iter().any(|f| f.file_name().unwrap() == "bootloader.hex"));
    }
}
