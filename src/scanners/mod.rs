//! # Scanners Module
//!
//! This module contains all scanning functionality for the Infinitium Signal platform.
//! It provides SBOM (Software Bill of Materials), HBOM (Hardware Bill of Materials),
//! repository analysis, and dependency resolution capabilities.

pub mod dependency_resolver;
pub mod hbom_scanner;
pub mod repo_analyzer;
pub mod sbom_scanner;

use crate::{config::ScanningConfig, error::Result};
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, path::Path, time::Duration};
use uuid::Uuid;

/// Re-export scanner types
pub use dependency_resolver::{DependencyResolver, DependencyTree};
pub use hbom_scanner::{HbomScanner, HardwareComponent};
pub use repo_analyzer::{RepoAnalyzer, RepositoryInfo};
pub use sbom_scanner::{SbomScanner, SoftwareComponent};

/// Scan types supported by the platform
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ScanType {
    /// Software Bill of Materials
    Sbom,
    /// Hardware Bill of Materials
    Hbom,
    /// Repository analysis
    Repository,
    /// Dependency analysis
    Dependencies,
    /// Combined scan (all types)
    Combined,
}

impl std::fmt::Display for ScanType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ScanType::Sbom => write!(f, "sbom"),
            ScanType::Hbom => write!(f, "hbom"),
            ScanType::Repository => write!(f, "repository"),
            ScanType::Dependencies => write!(f, "dependencies"),
            ScanType::Combined => write!(f, "combined"),
        }
    }
}

/// Scan request configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScanRequest {
    /// Unique scan identifier
    pub id: Uuid,
    /// Type of scan to perform
    pub scan_type: ScanType,
    /// Target path or URL to scan
    pub target: String,
    /// Scan options
    pub options: ScanOptions,
    /// Metadata for the scan
    pub metadata: HashMap<String, String>,
}

/// Scan configuration options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScanOptions {
    /// Include development dependencies
    pub include_dev_dependencies: bool,
    /// Maximum scan depth for dependency resolution
    pub max_depth: u32,
    /// Scan timeout in seconds
    pub timeout: u64,
    /// Include transitive dependencies
    pub include_transitive: bool,
    /// Exclude patterns (glob patterns)
    pub exclude_patterns: Vec<String>,
    /// Include patterns (glob patterns)
    pub include_patterns: Vec<String>,
    /// Output format preferences
    pub output_formats: Vec<OutputFormat>,
    /// Enable vulnerability scanning
    pub enable_vulnerability_scan: bool,
    /// Enable license scanning
    pub enable_license_scan: bool,
}

/// Output formats for scan results
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum OutputFormat {
    /// CycloneDX format
    CycloneDx,
    /// SPDX format
    Spdx,
    /// JSON format
    Json,
    /// XML format
    Xml,
    /// CSV format
    Csv,
    /// YAML format
    Yaml,
}

/// Scan result containing all discovered information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScanResult {
    /// Scan request that generated this result
    pub request: ScanRequest,
    /// Scan status
    pub status: ScanStatus,
    /// Start time
    pub started_at: chrono::DateTime<chrono::Utc>,
    /// End time
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
    /// Scan duration
    pub duration: Option<Duration>,
    /// Software components found
    pub software_components: Vec<SoftwareComponent>,
    /// Hardware components found
    pub hardware_components: Vec<HardwareComponent>,
    /// Repository information
    pub repository_info: Option<RepositoryInfo>,
    /// Dependency tree
    pub dependency_tree: Option<DependencyTree>,
    /// Vulnerabilities found
    pub vulnerabilities: Vec<VulnerabilityInfo>,
    /// License information
    pub licenses: Vec<LicenseInfo>,
    /// Scan errors and warnings
    pub issues: Vec<ScanIssue>,
    /// Additional metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Scan execution status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ScanStatus {
    /// Scan is queued
    Queued,
    /// Scan is in progress
    InProgress,
    /// Scan completed successfully
    Completed,
    /// Scan failed
    Failed,
    /// Scan was cancelled
    Cancelled,
    /// Scan timed out
    TimedOut,
}

/// Vulnerability information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VulnerabilityInfo {
    /// CVE identifier
    pub cve_id: String,
    /// Vulnerability severity
    pub severity: String,
    /// CVSS score
    pub cvss_score: Option<f64>,
    /// Description
    pub description: String,
    /// Affected component
    pub component: String,
    /// Fixed version (if available)
    pub fixed_version: Option<String>,
    /// References
    pub references: Vec<String>,
}

/// License information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LicenseInfo {
    /// License identifier (SPDX)
    pub id: String,
    /// License name
    pub name: String,
    /// License text URL
    pub url: Option<String>,
    /// Component using this license
    pub component: String,
    /// License compatibility
    pub compatibility: LicenseCompatibility,
}

/// License compatibility levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum LicenseCompatibility {
    /// Compatible with commercial use
    Commercial,
    /// Open source compatible
    OpenSource,
    /// Copyleft license
    Copyleft,
    /// Proprietary license
    Proprietary,
    /// Unknown compatibility
    Unknown,
}

/// Scan issues (errors, warnings, info)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScanIssue {
    /// Issue severity
    pub severity: IssueSeverity,
    /// Issue message
    pub message: String,
    /// File or component where issue occurred
    pub location: Option<String>,
    /// Issue code for categorization
    pub code: Option<String>,
}

/// Issue severity levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum IssueSeverity {
    /// Informational message
    Info,
    /// Warning message
    Warning,
    /// Error message
    Error,
    /// Critical error
    Critical,
}

/// Main scanner orchestrator
pub struct ScannerOrchestrator {
    config: ScanningConfig,
    sbom_scanner: SbomScanner,
    hbom_scanner: HbomScanner,
    repo_analyzer: RepoAnalyzer,
    dependency_resolver: DependencyResolver,
}

impl ScannerOrchestrator {
    /// Create new scanner orchestrator
    pub fn new(config: ScanningConfig) -> Self {
        Self {
            sbom_scanner: SbomScanner::new(&config),
            hbom_scanner: HbomScanner::new(&config),
            repo_analyzer: RepoAnalyzer::new(&config),
            dependency_resolver: DependencyResolver::new(&config),
            config,
        }
    }

    /// Execute a scan request
    pub async fn execute_scan(&self, request: ScanRequest) -> Result<ScanResult> {
        let start_time = chrono::Utc::now();
        
        let mut result = ScanResult {
            request: request.clone(),
            status: ScanStatus::InProgress,
            started_at: start_time,
            completed_at: None,
            duration: None,
            software_components: Vec::new(),
            hardware_components: Vec::new(),
            repository_info: None,
            dependency_tree: None,
            vulnerabilities: Vec::new(),
            licenses: Vec::new(),
            issues: Vec::new(),
            metadata: HashMap::new(),
        };

        // Execute scan based on type
        match request.scan_type {
            ScanType::Sbom => {
                self.execute_sbom_scan(&request, &mut result).await?;
            }
            ScanType::Hbom => {
                self.execute_hbom_scan(&request, &mut result).await?;
            }
            ScanType::Repository => {
                self.execute_repo_scan(&request, &mut result).await?;
            }
            ScanType::Dependencies => {
                self.execute_dependency_scan(&request, &mut result).await?;
            }
            ScanType::Combined => {
                self.execute_combined_scan(&request, &mut result).await?;
            }
        }

        // Finalize result
        let end_time = chrono::Utc::now();
        result.completed_at = Some(end_time);
        result.duration = Some(Duration::from_millis(
            (end_time - start_time).num_milliseconds() as u64,
        ));
        result.status = ScanStatus::Completed;

        Ok(result)
    }

    /// Execute SBOM scan
    async fn execute_sbom_scan(&self, request: &ScanRequest, result: &mut ScanResult) -> Result<()> {
        let target_path = Path::new(&request.target);
        let components = self.sbom_scanner.scan_project(target_path).await?;
        result.software_components = components;
        Ok(())
    }

    /// Execute HBOM scan
    async fn execute_hbom_scan(&self, request: &ScanRequest, result: &mut ScanResult) -> Result<()> {
        let target_path = Path::new(&request.target);
        let components = self.hbom_scanner.scan_hardware(target_path).await?;
        result.hardware_components = components;
        Ok(())
    }

    /// Execute repository scan
    async fn execute_repo_scan(&self, request: &ScanRequest, result: &mut ScanResult) -> Result<()> {
        let target_path = Path::new(&request.target);
        let repo_info = self.repo_analyzer.analyze_repository(target_path).await?;
        result.repository_info = Some(repo_info);
        Ok(())
    }

    /// Execute dependency scan
    async fn execute_dependency_scan(&self, request: &ScanRequest, result: &mut ScanResult) -> Result<()> {
        let target_path = Path::new(&request.target);
        let dependency_tree = self.dependency_resolver.resolve_dependencies(target_path).await?;
        result.dependency_tree = Some(dependency_tree);
        Ok(())
    }

    /// Execute combined scan (all scan types)
    async fn execute_combined_scan(&self, request: &ScanRequest, result: &mut ScanResult) -> Result<()> {
        // Execute all scan types
        self.execute_sbom_scan(request, result).await?;
        self.execute_hbom_scan(request, result).await?;
        self.execute_repo_scan(request, result).await?;
        self.execute_dependency_scan(request, result).await?;
        Ok(())
    }
}

impl Default for ScanOptions {
    fn default() -> Self {
        Self {
            include_dev_dependencies: false,
            max_depth: 10,
            timeout: 300,
            include_transitive: true,
            exclude_patterns: vec![
                "**/node_modules/**".to_string(),
                "**/target/**".to_string(),
                "**/.git/**".to_string(),
            ],
            include_patterns: vec!["**/*".to_string()],
            output_formats: vec![OutputFormat::Json, OutputFormat::CycloneDx],
            enable_vulnerability_scan: true,
            enable_license_scan: true,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_scan_type_display() {
        assert_eq!(ScanType::Sbom.to_string(), "sbom");
        assert_eq!(ScanType::Hbom.to_string(), "hbom");
        assert_eq!(ScanType::Repository.to_string(), "repository");
        assert_eq!(ScanType::Dependencies.to_string(), "dependencies");
        assert_eq!(ScanType::Combined.to_string(), "combined");
    }

    #[test]
    fn test_scan_options_default() {
        let options = ScanOptions::default();
        assert!(!options.include_dev_dependencies);
        assert_eq!(options.max_depth, 10);
        assert_eq!(options.timeout, 300);
        assert!(options.include_transitive);
        assert!(options.enable_vulnerability_scan);
        assert!(options.enable_license_scan);
    }

    #[test]
    fn test_scan_request_creation() {
        let request = ScanRequest {
            id: Uuid::new_v4(),
            scan_type: ScanType::Sbom,
            target: "/path/to/project".to_string(),
            options: ScanOptions::default(),
            metadata: HashMap::new(),
        };

        assert_eq!(request.scan_type, ScanType::Sbom);
        assert_eq!(request.target, "/path/to/project");
    }
}
