use crate::{config::ScanningConfig, error::Result};
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, path::{Path, PathBuf}};
use tokio::fs;
use tracing::{debug, info, instrument};

/// Repository analyzer for extracting metadata and structure information
pub struct RepoAnalyzer {
    config: ScanningConfig,
}

/// Repository information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RepositoryInfo {
    /// Repository name
    pub name: String,
    /// Repository description
    pub description: Option<String>,
    /// Repository URL
    pub url: Option<String>,
    /// Primary language
    pub primary_language: Option<String>,
    /// Languages detected with percentages
    pub languages: HashMap<String, f64>,
    /// License information
    pub license: Option<String>,
    /// README content
    pub readme: Option<String>,
    /// Contributors count
    pub contributors_count: Option<u32>,
    /// Repository size in bytes
    pub size_bytes: u64,
    /// File count
    pub file_count: u32,
    /// Directory count
    pub directory_count: u32,
    /// Git information
    pub git_info: Option<GitInfo>,
    /// Build configuration
    pub build_config: Option<BuildConfig>,
    /// Documentation files
    pub documentation: Vec<String>,
    /// Configuration files
    pub config_files: Vec<String>,
    /// Test directories
    pub test_directories: Vec<String>,
    /// Additional metadata
    pub metadata: HashMap<String, String>,
}

/// Git repository information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GitInfo {
    /// Current branch
    pub current_branch: Option<String>,
    /// Remote URL
    pub remote_url: Option<String>,
    /// Last commit hash
    pub last_commit: Option<String>,
    /// Last commit date
    pub last_commit_date: Option<chrono::DateTime<chrono::Utc>>,
    /// Total commits
    pub total_commits: Option<u32>,
    /// Contributors
    pub contributors: Vec<String>,
}

/// Build configuration information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BuildConfig {
    /// Build system type
    pub build_system: String,
    /// Build files found
    pub build_files: Vec<String>,
    /// CI/CD configuration
    pub ci_config: Vec<String>,
    /// Docker configuration
    pub docker_config: Vec<String>,
}

impl RepoAnalyzer {
    /// Create new repository analyzer
    pub fn new(config: &ScanningConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }

    /// Analyze repository structure and metadata
    #[instrument(skip(self), fields(repo_path = %repo_path.display()))]
    pub async fn analyze_repository(&self, repo_path: &Path) -> Result<RepositoryInfo> {
        info!("Starting repository analysis");

        let mut repo_info = RepositoryInfo {
            name: repo_path.file_name()
                .and_then(|name| name.to_str())
                .unwrap_or("unknown")
                .to_string(),
            description: None,
            url: None,
            primary_language: None,
            languages: HashMap::new(),
            license: None,
            readme: None,
            contributors_count: None,
            size_bytes: 0,
            file_count: 0,
            directory_count: 0,
            git_info: None,
            build_config: None,
            documentation: Vec::new(),
            config_files: Vec::new(),
            test_directories: Vec::new(),
            metadata: HashMap::new(),
        };

        // Analyze directory structure
        self.analyze_directory_structure(repo_path, &mut repo_info).await?;

        // Detect languages
        self.detect_languages(repo_path, &mut repo_info).await?;

        // Find and parse README
        self.find_readme(repo_path, &mut repo_info).await?;

        // Find license
        self.find_license(repo_path, &mut repo_info).await?;

        // Analyze Git repository if present
        if repo_path.join(".git").exists() {
            self.analyze_git_info(repo_path, &mut repo_info).await?;
        }

        // Analyze build configuration
        self.analyze_build_config(repo_path, &mut repo_info).await?;

        // Find documentation
        self.find_documentation(repo_path, &mut repo_info).await?;

        // Find configuration files
        self.find_config_files(repo_path, &mut repo_info).await?;

        // Find test directories
        self.find_test_directories(repo_path, &mut repo_info).await?;

        info!(
            files = repo_info.file_count,
            directories = repo_info.directory_count,
            size_mb = repo_info.size_bytes / 1024 / 1024,
            "Repository analysis completed"
        );

        Ok(repo_info)
    }

    /// Analyze directory structure
    async fn analyze_directory_structure(&self, repo_path: &Path, repo_info: &mut RepositoryInfo) -> Result<()> {
        let mut total_size = 0u64;
        let mut file_count = 0u32;
        let mut dir_count = 0u32;

        let mut stack = vec![repo_path.to_path_buf()];

        while let Some(current_path) = stack.pop() {
            if current_path.is_dir() {
                dir_count += 1;
                
                let mut entries = fs::read_dir(&current_path).await?;
                while let Some(entry) = entries.next_entry().await? {
                    let path = entry.path();
                    
                    if path.is_dir() {
                        // Skip hidden directories and common ignore patterns
                        if let Some(dir_name) = path.file_name().and_then(|n| n.to_str()) {
                            if !dir_name.starts_with('.') 
                                && !["node_modules", "target", "build", "__pycache__"].contains(&dir_name) {
                                stack.push(path);
                            }
                        }
                    } else if path.is_file() {
                        file_count += 1;
                        if let Ok(metadata) = entry.metadata().await {
                            total_size += metadata.len();
                        }
                    }
                }
            }
        }

        repo_info.size_bytes = total_size;
        repo_info.file_count = file_count;
        repo_info.directory_count = dir_count;

        Ok(())
    }

    /// Detect programming languages in the repository
    async fn detect_languages(&self, repo_path: &Path, repo_info: &mut RepositoryInfo) -> Result<()> {
        let mut language_counts: HashMap<String, u64> = HashMap::new();
        let mut total_lines = 0u64;

        let language_extensions = [
            ("Rust", vec!["rs"]),
            ("JavaScript", vec!["js", "jsx", "ts", "tsx"]),
            ("Python", vec!["py", "pyx", "pyi"]),
            ("Java", vec!["java"]),
            ("Go", vec!["go"]),
            ("C", vec!["c", "h"]),
            ("C++", vec!["cpp", "cxx", "cc", "hpp", "hxx"]),
            ("C#", vec!["cs"]),
            ("PHP", vec!["php"]),
            ("Ruby", vec!["rb"]),
            ("Shell", vec!["sh", "bash", "zsh"]),
            ("YAML", vec!["yaml", "yml"]),
            ("JSON", vec!["json"]),
            ("XML", vec!["xml"]),
            ("HTML", vec!["html", "htm"]),
            ("CSS", vec!["css", "scss", "sass"]),
            ("Dockerfile", vec!["dockerfile"]),
        ];

        let mut stack = vec![repo_path.to_path_buf()];

        while let Some(current_path) = stack.pop() {
            if current_path.is_dir() {
                let mut entries = fs::read_dir(&current_path).await?;
                while let Some(entry) = entries.next_entry().await? {
                    let path = entry.path();
                    
                    if path.is_dir() {
                        if let Some(dir_name) = path.file_name().and_then(|n| n.to_str()) {
                            if !dir_name.starts_with('.') 
                                && !["node_modules", "target", "build", "__pycache__"].contains(&dir_name) {
                                stack.push(path);
                            }
                        }
                    } else if path.is_file() {
                        if let Some(extension) = path.extension().and_then(|ext| ext.to_str()) {
                            for (language, extensions) in &language_extensions {
                                if extensions.contains(&extension.to_lowercase().as_str()) {
                                    // Count lines in file
                                    if let Ok(content) = fs::read_to_string(&path).await {
                                        let lines = content.lines().count() as u64;
                                        *language_counts.entry(language.to_string()).or_insert(0) += lines;
                                        total_lines += lines;
                                    }
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }

        // Calculate percentages
        for (language, lines) in language_counts {
            let percentage = if total_lines > 0 {
                (lines as f64 / total_lines as f64) * 100.0
            } else {
                0.0
            };
            repo_info.languages.insert(language, percentage);
        }

        // Determine primary language
        repo_info.primary_language = repo_info.languages
            .iter()
            .max_by(|a, b| a.1.partial_cmp(b.1).unwrap())
            .map(|(lang, _)| lang.clone());

        Ok(())
    }

    /// Find and read README file
    async fn find_readme(&self, repo_path: &Path, repo_info: &mut RepositoryInfo) -> Result<()> {
        let readme_names = ["README.md", "README.txt", "README.rst", "README", "readme.md", "readme.txt"];

        for readme_name in &readme_names {
            let readme_path = repo_path.join(readme_name);
            if readme_path.exists() {
                if let Ok(content) = fs::read_to_string(&readme_path).await {
                    repo_info.readme = Some(content);
                    
                    // Try to extract description from README
                    if repo_info.description.is_none() {
                        repo_info.description = self.extract_description_from_readme(&content);
                    }
                }
                break;
            }
        }

        Ok(())
    }

    /// Extract description from README content
    fn extract_description_from_readme(&self, content: &str) -> Option<String> {
        let lines: Vec<&str> = content.lines().collect();
        
        // Look for the first non-empty line after the title
        let mut found_title = false;
        for line in lines {
            let trimmed = line.trim();
            
            if trimmed.starts_with('#') {
                found_title = true;
                continue;
            }
            
            if found_title && !trimmed.is_empty() && !trimmed.starts_with('#') {
                return Some(trimmed.to_string());
            }
        }
        
        None
    }

    /// Find license file
    async fn find_license(&self, repo_path: &Path, repo_info: &mut RepositoryInfo) -> Result<()> {
        let license_names = ["LICENSE", "LICENSE.txt", "LICENSE.md", "COPYING", "license", "license.txt"];

        for license_name in &license_names {
            let license_path = repo_path.join(license_name);
            if license_path.exists() {
                if let Ok(content) = fs::read_to_string(&license_path).await {
                    repo_info.license = self.detect_license_type(&content);
                }
                break;
            }
        }

        Ok(())
    }

    /// Detect license type from content
    fn detect_license_type(&self, content: &str) -> Option<String> {
        let content_lower = content.to_lowercase();
        
        if content_lower.contains("apache license") {
            Some("Apache-2.0".to_string())
        } else if content_lower.contains("mit license") {
            Some("MIT".to_string())
        } else if content_lower.contains("gnu general public license") {
            if content_lower.contains("version 3") {
                Some("GPL-3.0".to_string())
            } else if content_lower.contains("version 2") {
                Some("GPL-2.0".to_string())
            } else {
                Some("GPL".to_string())
            }
        } else if content_lower.contains("bsd license") {
            Some("BSD".to_string())
        } else {
            Some("Unknown".to_string())
        }
    }

    /// Analyze Git repository information
    async fn analyze_git_info(&self, _repo_path: &Path, _repo_info: &mut RepositoryInfo) -> Result<()> {
        // TODO: Implement Git analysis using git2 crate or git commands
        Ok(())
    }

    /// Analyze build configuration
    async fn analyze_build_config(&self, repo_path: &Path, repo_info: &mut RepositoryInfo) -> Result<()> {
        let mut build_config = BuildConfig {
            build_system: "unknown".to_string(),
            build_files: Vec::new(),
            ci_config: Vec::new(),
            docker_config: Vec::new(),
        };

        // Check for various build systems
        let build_files = [
            ("Cargo.toml", "Cargo"),
            ("package.json", "npm"),
            ("pom.xml", "Maven"),
            ("build.gradle", "Gradle"),
            ("Makefile", "Make"),
            ("CMakeLists.txt", "CMake"),
            ("setup.py", "Python setuptools"),
            ("pyproject.toml", "Python build"),
        ];

        for (file_name, build_system) in &build_files {
            let file_path = repo_path.join(file_name);
            if file_path.exists() {
                build_config.build_system = build_system.to_string();
                build_config.build_files.push(file_name.to_string());
            }
        }

        // Check for CI/CD configuration
        let ci_files = [
            ".github/workflows",
            ".gitlab-ci.yml",
            ".travis.yml",
            "azure-pipelines.yml",
            "Jenkinsfile",
        ];

        for ci_file in &ci_files {
            let ci_path = repo_path.join(ci_file);
            if ci_path.exists() {
                build_config.ci_config.push(ci_file.to_string());
            }
        }

        // Check for Docker configuration
        let docker_files = ["Dockerfile", "docker-compose.yml", "docker-compose.yaml"];

        for docker_file in &docker_files {
            let docker_path = repo_path.join(docker_file);
            if docker_path.exists() {
                build_config.docker_config.push(docker_file.to_string());
            }
        }

        repo_info.build_config = Some(build_config);
        Ok(())
    }

    /// Find documentation files
    async fn find_documentation(&self, repo_path: &Path, repo_info: &mut RepositoryInfo) -> Result<()> {
        let doc_patterns = ["docs", "doc", "documentation"];
        let doc_extensions = ["md", "rst", "txt", "adoc"];

        for pattern in &doc_patterns {
            let doc_dir = repo_path.join(pattern);
            if doc_dir.exists() && doc_dir.is_dir() {
                repo_info.documentation.push(pattern.to_string());
            }
        }

        // Find individual documentation files
        let mut entries = fs::read_dir(repo_path).await?;
        while let Some(entry) = entries.next_entry().await? {
            let path = entry.path();
            if path.is_file() {
                if let Some(extension) = path.extension().and_then(|ext| ext.to_str()) {
                    if doc_extensions.contains(&extension.to_lowercase().as_str()) {
                        if let Some(file_name) = path.file_name().and_then(|name| name.to_str()) {
                            if file_name.to_lowercase().contains("doc") 
                                || file_name.to_lowercase().contains("guide")
                                || file_name.to_lowercase().contains("manual") {
                                repo_info.documentation.push(file_name.to_string());
                            }
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// Find configuration files
    async fn find_config_files(&self, repo_path: &Path, repo_info: &mut RepositoryInfo) -> Result<()> {
        let config_files = [
            ".env", ".env.example", "config.yaml", "config.yml", "config.json",
            "settings.yaml", "settings.yml", "settings.json", ".editorconfig",
            ".gitignore", ".dockerignore", "tsconfig.json", "babel.config.js",
        ];

        for config_file in &config_files {
            let config_path = repo_path.join(config_file);
            if config_path.exists() {
                repo_info.config_files.push(config_file.to_string());
            }
        }

        Ok(())
    }

    /// Find test directories
    async fn find_test_directories(&self, repo_path: &Path, repo_info: &mut RepositoryInfo) -> Result<()> {
        let test_patterns = ["test", "tests", "spec", "specs", "__tests__"];

        for pattern in &test_patterns {
            let test_dir = repo_path.join(pattern);
            if test_dir.exists() && test_dir.is_dir() {
                repo_info.test_directories.push(pattern.to_string());
            }
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_detect_license_type() {
        let config = ScanningConfig::default();
        let analyzer = RepoAnalyzer::new(&config);

        let mit_license = "MIT License\n\nCopyright (c) 2024";
        assert_eq!(analyzer.detect_license_type(mit_license), Some("MIT".to_string()));

        let apache_license = "Apache License\nVersion 2.0";
        assert_eq!(analyzer.detect_license_type(apache_license), Some("Apache-2.0".to_string()));
    }

    #[tokio::test]
    async fn test_extract_description_from_readme() {
        let config = ScanningConfig::default();
        let analyzer = RepoAnalyzer::new(&config);

        let readme_content = "# My Project\n\nThis is a great project that does amazing things.\n\n## Installation";
        let description = analyzer.extract_description_from_readme(readme_content);
        assert_eq!(description, Some("This is a great project that does amazing things.".to_string()));
    }
}
