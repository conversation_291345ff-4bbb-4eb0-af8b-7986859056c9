use crate::{
    config::ScanningConfig,
    error::{InfinitumError, Result},
    metrics::ScanMetrics,
};
use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    path::{Path, PathBuf},
    process::Command,
};
use tokio::{fs, time::timeout};
use tracing::{debug, info, instrument, warn};
use walkdir::WalkDir;

/// Software Bill of Materials scanner
pub struct SbomScanner {
    config: ScanningConfig,
}

/// Software component information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SoftwareComponent {
    /// Component name
    pub name: String,
    /// Component version
    pub version: String,
    /// Package type (e.g., "cargo", "npm", "pip")
    pub package_type: String,
    /// Package manager
    pub package_manager: String,
    /// Component description
    pub description: Option<String>,
    /// Homepage URL
    pub homepage: Option<String>,
    /// Repository URL
    pub repository: Option<String>,
    /// License information
    pub license: Option<String>,
    /// Dependencies
    pub dependencies: Vec<String>,
    /// File path where component was found
    pub file_path: PathBuf,
    /// Component hash/checksum
    pub hash: Option<String>,
    /// Component scope (e.g., "runtime", "development", "test")
    pub scope: ComponentScope,
    /// Additional metadata
    pub metadata: HashMap<String, String>,
}

/// Component scope enumeration
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ComponentScope {
    /// Runtime dependency
    Runtime,
    /// Development dependency
    Development,
    /// Test dependency
    Test,
    /// Build dependency
    Build,
    /// Optional dependency
    Optional,
    /// Unknown scope
    Unknown,
}

/// Project type detection
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ProjectType {
    Rust,
    JavaScript,
    Python,
    Java,
    Go,
    CSharp,
    Cpp,
    Unknown,
}

impl SbomScanner {
    /// Create new SBOM scanner
    pub fn new(config: &ScanningConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }

    /// Scan project for software components
    #[instrument(skip(self), fields(project_path = %project_path.display()))]
    pub async fn scan_project(&self, project_path: &Path) -> Result<Vec<SoftwareComponent>> {
        info!("Starting SBOM scan");
        ScanMetrics::scan_started("sbom");

        let start_time = std::time::Instant::now();

        // Detect project type
        let project_type = self.detect_project_type(project_path).await?;
        debug!("Detected project type: {:?}", project_type);

        // Scan based on project type
        let components = match project_type {
            ProjectType::Rust => self.scan_rust_project(project_path).await?,
            ProjectType::JavaScript => self.scan_javascript_project(project_path).await?,
            ProjectType::Python => self.scan_python_project(project_path).await?,
            ProjectType::Java => self.scan_java_project(project_path).await?,
            ProjectType::Go => self.scan_go_project(project_path).await?,
            ProjectType::CSharp => self.scan_csharp_project(project_path).await?,
            ProjectType::Cpp => self.scan_cpp_project(project_path).await?,
            ProjectType::Unknown => {
                warn!("Unknown project type, attempting generic scan");
                self.scan_generic_project(project_path).await?
            }
        };

        let duration = start_time.elapsed();
        ScanMetrics::scan_completed("sbom", duration);

        info!(
            components_found = components.len(),
            duration_ms = duration.as_millis(),
            "SBOM scan completed"
        );

        Ok(components)
    }

    /// Detect project type based on files present
    async fn detect_project_type(&self, project_path: &Path) -> Result<ProjectType> {
        // Check for Rust project
        if project_path.join("Cargo.toml").exists() {
            return Ok(ProjectType::Rust);
        }

        // Check for JavaScript/Node.js project
        if project_path.join("package.json").exists() {
            return Ok(ProjectType::JavaScript);
        }

        // Check for Python project
        if project_path.join("requirements.txt").exists()
            || project_path.join("pyproject.toml").exists()
            || project_path.join("setup.py").exists()
            || project_path.join("Pipfile").exists()
        {
            return Ok(ProjectType::Python);
        }

        // Check for Java project
        if project_path.join("pom.xml").exists()
            || project_path.join("build.gradle").exists()
            || project_path.join("build.gradle.kts").exists()
        {
            return Ok(ProjectType::Java);
        }

        // Check for Go project
        if project_path.join("go.mod").exists() {
            return Ok(ProjectType::Go);
        }

        // Check for C# project
        if project_path.join("*.csproj").exists() || project_path.join("*.sln").exists() {
            return Ok(ProjectType::CSharp);
        }

        // Check for C++ project
        if project_path.join("CMakeLists.txt").exists()
            || project_path.join("Makefile").exists()
            || project_path.join("conanfile.txt").exists()
        {
            return Ok(ProjectType::Cpp);
        }

        Ok(ProjectType::Unknown)
    }

    /// Scan Rust project using Cargo
    async fn scan_rust_project(&self, project_path: &Path) -> Result<Vec<SoftwareComponent>> {
        let cargo_toml_path = project_path.join("Cargo.toml");
        let cargo_lock_path = project_path.join("Cargo.lock");

        let mut components = Vec::new();

        // Parse Cargo.toml
        if cargo_toml_path.exists() {
            let cargo_toml_content = fs::read_to_string(&cargo_toml_path).await?;
            let cargo_toml: toml::Value = toml::from_str(&cargo_toml_content)?;

            // Extract dependencies
            if let Some(dependencies) = cargo_toml.get("dependencies").and_then(|d| d.as_table()) {
                for (name, dep_info) in dependencies {
                    let component = self.parse_rust_dependency(name, dep_info, &cargo_toml_path, ComponentScope::Runtime)?;
                    components.push(component);
                }
            }

            // Extract dev dependencies
            if let Some(dev_dependencies) = cargo_toml.get("dev-dependencies").and_then(|d| d.as_table()) {
                for (name, dep_info) in dev_dependencies {
                    let component = self.parse_rust_dependency(name, dep_info, &cargo_toml_path, ComponentScope::Development)?;
                    components.push(component);
                }
            }

            // Extract build dependencies
            if let Some(build_dependencies) = cargo_toml.get("build-dependencies").and_then(|d| d.as_table()) {
                for (name, dep_info) in build_dependencies {
                    let component = self.parse_rust_dependency(name, dep_info, &cargo_toml_path, ComponentScope::Build)?;
                    components.push(component);
                }
            }
        }

        // Parse Cargo.lock for exact versions
        if cargo_lock_path.exists() {
            let cargo_lock_content = fs::read_to_string(&cargo_lock_path).await?;
            let cargo_lock: toml::Value = toml::from_str(&cargo_lock_content)?;

            if let Some(packages) = cargo_lock.get("package").and_then(|p| p.as_array()) {
                for package in packages {
                    if let Some(package_table) = package.as_table() {
                        if let (Some(name), Some(version)) = (
                            package_table.get("name").and_then(|n| n.as_str()),
                            package_table.get("version").and_then(|v| v.as_str()),
                        ) {
                            // Update existing component with exact version
                            if let Some(component) = components.iter_mut().find(|c| c.name == name) {
                                component.version = version.to_string();
                            }
                        }
                    }
                }
            }
        }

        Ok(components)
    }

    /// Parse Rust dependency from Cargo.toml
    fn parse_rust_dependency(
        &self,
        name: &str,
        dep_info: &toml::Value,
        file_path: &Path,
        scope: ComponentScope,
    ) -> Result<SoftwareComponent> {
        let version = match dep_info {
            toml::Value::String(version) => version.clone(),
            toml::Value::Table(table) => {
                table.get("version")
                    .and_then(|v| v.as_str())
                    .unwrap_or("*")
                    .to_string()
            }
            _ => "*".to_string(),
        };

        Ok(SoftwareComponent {
            name: name.to_string(),
            version,
            package_type: "crate".to_string(),
            package_manager: "cargo".to_string(),
            description: None,
            homepage: None,
            repository: None,
            license: None,
            dependencies: Vec::new(),
            file_path: file_path.to_path_buf(),
            hash: None,
            scope,
            metadata: HashMap::new(),
        })
    }

    /// Scan JavaScript/Node.js project
    async fn scan_javascript_project(&self, project_path: &Path) -> Result<Vec<SoftwareComponent>> {
        let package_json_path = project_path.join("package.json");
        let mut components = Vec::new();

        if package_json_path.exists() {
            let package_json_content = fs::read_to_string(&package_json_path).await?;
            let package_json: serde_json::Value = serde_json::from_str(&package_json_content)?;

            // Parse dependencies
            if let Some(dependencies) = package_json.get("dependencies").and_then(|d| d.as_object()) {
                for (name, version) in dependencies {
                    let component = SoftwareComponent {
                        name: name.clone(),
                        version: version.as_str().unwrap_or("*").to_string(),
                        package_type: "npm".to_string(),
                        package_manager: "npm".to_string(),
                        description: None,
                        homepage: None,
                        repository: None,
                        license: None,
                        dependencies: Vec::new(),
                        file_path: package_json_path.clone(),
                        hash: None,
                        scope: ComponentScope::Runtime,
                        metadata: HashMap::new(),
                    };
                    components.push(component);
                }
            }

            // Parse dev dependencies
            if let Some(dev_dependencies) = package_json.get("devDependencies").and_then(|d| d.as_object()) {
                for (name, version) in dev_dependencies {
                    let component = SoftwareComponent {
                        name: name.clone(),
                        version: version.as_str().unwrap_or("*").to_string(),
                        package_type: "npm".to_string(),
                        package_manager: "npm".to_string(),
                        description: None,
                        homepage: None,
                        repository: None,
                        license: None,
                        dependencies: Vec::new(),
                        file_path: package_json_path.clone(),
                        hash: None,
                        scope: ComponentScope::Development,
                        metadata: HashMap::new(),
                    };
                    components.push(component);
                }
            }
        }

        Ok(components)
    }

    /// Scan Python project
    async fn scan_python_project(&self, project_path: &Path) -> Result<Vec<SoftwareComponent>> {
        let mut components = Vec::new();

        // Check requirements.txt
        let requirements_path = project_path.join("requirements.txt");
        if requirements_path.exists() {
            let requirements_content = fs::read_to_string(&requirements_path).await?;
            for line in requirements_content.lines() {
                if let Some(component) = self.parse_python_requirement(line, &requirements_path)? {
                    components.push(component);
                }
            }
        }

        // Check pyproject.toml
        let pyproject_path = project_path.join("pyproject.toml");
        if pyproject_path.exists() {
            let pyproject_content = fs::read_to_string(&pyproject_path).await?;
            let pyproject: toml::Value = toml::from_str(&pyproject_content)?;

            if let Some(dependencies) = pyproject
                .get("project")
                .and_then(|p| p.get("dependencies"))
                .and_then(|d| d.as_array())
            {
                for dep in dependencies {
                    if let Some(dep_str) = dep.as_str() {
                        if let Some(component) = self.parse_python_requirement(dep_str, &pyproject_path)? {
                            components.push(component);
                        }
                    }
                }
            }
        }

        Ok(components)
    }

    /// Parse Python requirement string
    fn parse_python_requirement(&self, requirement: &str, file_path: &Path) -> Result<Option<SoftwareComponent>> {
        let requirement = requirement.trim();
        if requirement.is_empty() || requirement.starts_with('#') {
            return Ok(None);
        }

        // Simple parsing - can be enhanced with proper requirement parsing
        let parts: Vec<&str> = requirement.split(&['=', '>', '<', '!', '~'][..]).collect();
        if parts.is_empty() {
            return Ok(None);
        }

        let name = parts[0].trim();
        let version = if parts.len() > 1 {
            parts[1].trim().to_string()
        } else {
            "*".to_string()
        };

        Ok(Some(SoftwareComponent {
            name: name.to_string(),
            version,
            package_type: "pypi".to_string(),
            package_manager: "pip".to_string(),
            description: None,
            homepage: None,
            repository: None,
            license: None,
            dependencies: Vec::new(),
            file_path: file_path.to_path_buf(),
            hash: None,
            scope: ComponentScope::Runtime,
            metadata: HashMap::new(),
        }))
    }

    /// Scan Java project (placeholder)
    async fn scan_java_project(&self, _project_path: &Path) -> Result<Vec<SoftwareComponent>> {
        // TODO: Implement Java project scanning (Maven, Gradle)
        Ok(Vec::new())
    }

    /// Scan Go project (placeholder)
    async fn scan_go_project(&self, _project_path: &Path) -> Result<Vec<SoftwareComponent>> {
        // TODO: Implement Go project scanning (go.mod)
        Ok(Vec::new())
    }

    /// Scan C# project (placeholder)
    async fn scan_csharp_project(&self, _project_path: &Path) -> Result<Vec<SoftwareComponent>> {
        // TODO: Implement C# project scanning (.csproj, packages.config)
        Ok(Vec::new())
    }

    /// Scan C++ project (placeholder)
    async fn scan_cpp_project(&self, _project_path: &Path) -> Result<Vec<SoftwareComponent>> {
        // TODO: Implement C++ project scanning (CMake, Conan)
        Ok(Vec::new())
    }

    /// Generic project scan (fallback)
    async fn scan_generic_project(&self, project_path: &Path) -> Result<Vec<SoftwareComponent>> {
        let mut components = Vec::new();

        // Walk through project directory and look for known dependency files
        for entry in WalkDir::new(project_path)
            .max_depth(3)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            let file_name = entry.file_name().to_string_lossy();
            
            match file_name.as_ref() {
                "Cargo.toml" => {
                    let rust_components = self.scan_rust_project(entry.path().parent().unwrap()).await?;
                    components.extend(rust_components);
                }
                "package.json" => {
                    let js_components = self.scan_javascript_project(entry.path().parent().unwrap()).await?;
                    components.extend(js_components);
                }
                "requirements.txt" | "pyproject.toml" => {
                    let python_components = self.scan_python_project(entry.path().parent().unwrap()).await?;
                    components.extend(python_components);
                }
                _ => {}
            }
        }

        Ok(components)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use tokio::fs;

    #[tokio::test]
    async fn test_detect_rust_project() {
        let temp_dir = TempDir::new().unwrap();
        let project_path = temp_dir.path();

        // Create Cargo.toml
        fs::write(project_path.join("Cargo.toml"), "[package]\nname = \"test\"").await.unwrap();

        let config = ScanningConfig::default();
        let scanner = SbomScanner::new(&config);
        let project_type = scanner.detect_project_type(project_path).await.unwrap();

        assert_eq!(project_type, ProjectType::Rust);
    }

    #[tokio::test]
    async fn test_detect_javascript_project() {
        let temp_dir = TempDir::new().unwrap();
        let project_path = temp_dir.path();

        // Create package.json
        fs::write(project_path.join("package.json"), r#"{"name": "test"}"#).await.unwrap();

        let config = ScanningConfig::default();
        let scanner = SbomScanner::new(&config);
        let project_type = scanner.detect_project_type(project_path).await.unwrap();

        assert_eq!(project_type, ProjectType::JavaScript);
    }

    #[test]
    fn test_parse_python_requirement() {
        let config = ScanningConfig::default();
        let scanner = SbomScanner::new(&config);
        let file_path = Path::new("requirements.txt");

        let component = scanner.parse_python_requirement("requests==2.28.1", file_path).unwrap();
        assert!(component.is_some());
        
        let component = component.unwrap();
        assert_eq!(component.name, "requests");
        assert_eq!(component.version, "=2.28.1");
        assert_eq!(component.package_type, "pypi");
    }
}
