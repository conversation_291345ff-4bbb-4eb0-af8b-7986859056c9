//! File system utilities for safe file operations

use crate::error::{Result, InfinitiumError};
use std::fs::{self, File, OpenOptions};
use std::io::{self, Read, Write, BufReader, BufWriter};
use std::path::{Path, PathBuf};
use walkdir::WalkDir;
use tempfile::{TempDir, NamedTempFile};
use serde::{Deserialize, Serialize};

/// File metadata information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileMetadata {
    pub path: PathBuf,
    pub size: u64,
    pub modified: Option<std::time::SystemTime>,
    pub is_file: bool,
    pub is_dir: bool,
    pub permissions: u32,
}

/// File operation result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileOperationResult {
    pub success: bool,
    pub path: PathBuf,
    pub message: String,
    pub bytes_processed: Option<u64>,
}

/// Safely read file contents to string
pub fn read_file_to_string<P: AsRef<Path>>(path: P) -> Result<String> {
    let path = path.as_ref();
    
    if !path.exists() {
        return Err(InfinitiumError::FileNotFound(path.to_path_buf()));
    }
    
    if !path.is_file() {
        return Err(InfinitiumError::InvalidFileType(path.to_path_buf()));
    }
    
    fs::read_to_string(path).map_err(InfinitiumError::from)
}

/// Safely read file contents to bytes
pub fn read_file_to_bytes<P: AsRef<Path>>(path: P) -> Result<Vec<u8>> {
    let path = path.as_ref();
    
    if !path.exists() {
        return Err(InfinitiumError::FileNotFound(path.to_path_buf()));
    }
    
    if !path.is_file() {
        return Err(InfinitiumError::InvalidFileType(path.to_path_buf()));
    }
    
    fs::read(path).map_err(InfinitiumError::from)
}

/// Safely write string to file
pub fn write_string_to_file<P: AsRef<Path>>(path: P, content: &str) -> Result<FileOperationResult> {
    let path = path.as_ref();
    
    // Create parent directories if they don't exist
    if let Some(parent) = path.parent() {
        fs::create_dir_all(parent)?;
    }
    
    let bytes_written = content.len() as u64;
    fs::write(path, content)?;
    
    Ok(FileOperationResult {
        success: true,
        path: path.to_path_buf(),
        message: "File written successfully".to_string(),
        bytes_processed: Some(bytes_written),
    })
}

/// Safely write bytes to file
pub fn write_bytes_to_file<P: AsRef<Path>>(path: P, content: &[u8]) -> Result<FileOperationResult> {
    let path = path.as_ref();
    
    // Create parent directories if they don't exist
    if let Some(parent) = path.parent() {
        fs::create_dir_all(parent)?;
    }
    
    let bytes_written = content.len() as u64;
    fs::write(path, content)?;
    
    Ok(FileOperationResult {
        success: true,
        path: path.to_path_buf(),
        message: "File written successfully".to_string(),
        bytes_processed: Some(bytes_written),
    })
}

/// Get file metadata
pub fn get_file_metadata<P: AsRef<Path>>(path: P) -> Result<FileMetadata> {
    let path = path.as_ref();
    let metadata = fs::metadata(path)?;
    
    Ok(FileMetadata {
        path: path.to_path_buf(),
        size: metadata.len(),
        modified: metadata.modified().ok(),
        is_file: metadata.is_file(),
        is_dir: metadata.is_dir(),
        permissions: get_permissions(&metadata),
    })
}

/// Get file permissions (cross-platform)
#[cfg(unix)]
fn get_permissions(metadata: &fs::Metadata) -> u32 {
    use std::os::unix::fs::PermissionsExt;
    metadata.permissions().mode()
}

#[cfg(windows)]
fn get_permissions(_metadata: &fs::Metadata) -> u32 {
    // Windows doesn't have Unix-style permissions
    0o644
}

/// Create directory recursively
pub fn create_dir_all<P: AsRef<Path>>(path: P) -> Result<FileOperationResult> {
    let path = path.as_ref();
    fs::create_dir_all(path)?;
    
    Ok(FileOperationResult {
        success: true,
        path: path.to_path_buf(),
        message: "Directory created successfully".to_string(),
        bytes_processed: None,
    })
}

/// Remove file or directory recursively
pub fn remove_path<P: AsRef<Path>>(path: P) -> Result<FileOperationResult> {
    let path = path.as_ref();
    
    if !path.exists() {
        return Err(InfinitiumError::FileNotFound(path.to_path_buf()));
    }
    
    if path.is_dir() {
        fs::remove_dir_all(path)?;
    } else {
        fs::remove_file(path)?;
    }
    
    Ok(FileOperationResult {
        success: true,
        path: path.to_path_buf(),
        message: "Path removed successfully".to_string(),
        bytes_processed: None,
    })
}

/// Copy file or directory
pub fn copy_path<P: AsRef<Path>>(from: P, to: P) -> Result<FileOperationResult> {
    let from = from.as_ref();
    let to = to.as_ref();
    
    if !from.exists() {
        return Err(InfinitiumError::FileNotFound(from.to_path_buf()));
    }
    
    let bytes_copied = if from.is_file() {
        fs::copy(from, to)?
    } else {
        copy_dir_all(from, to)?
    };
    
    Ok(FileOperationResult {
        success: true,
        path: to.to_path_buf(),
        message: "Path copied successfully".to_string(),
        bytes_processed: Some(bytes_copied),
    })
}

/// Copy directory recursively
fn copy_dir_all(src: &Path, dst: &Path) -> io::Result<u64> {
    fs::create_dir_all(dst)?;
    let mut total_bytes = 0;
    
    for entry in fs::read_dir(src)? {
        let entry = entry?;
        let ty = entry.file_type()?;
        
        if ty.is_dir() {
            total_bytes += copy_dir_all(&entry.path(), &dst.join(entry.file_name()))?;
        } else {
            total_bytes += fs::copy(&entry.path(), &dst.join(entry.file_name()))?;
        }
    }
    
    Ok(total_bytes)
}

/// Find files matching a pattern
pub fn find_files<P: AsRef<Path>>(root: P, pattern: &str) -> Result<Vec<PathBuf>> {
    let root = root.as_ref();
    let mut files = Vec::new();
    
    for entry in WalkDir::new(root).into_iter().filter_map(|e| e.ok()) {
        if entry.file_type().is_file() {
            if let Some(file_name) = entry.file_name().to_str() {
                if file_name.contains(pattern) {
                    files.push(entry.path().to_path_buf());
                }
            }
        }
    }
    
    Ok(files)
}

/// Create temporary file
pub fn create_temp_file() -> Result<NamedTempFile> {
    NamedTempFile::new().map_err(InfinitiumError::from)
}

/// Create temporary directory
pub fn create_temp_dir() -> Result<TempDir> {
    TempDir::new().map_err(InfinitiumError::from)
}

/// Check if path is safe (no directory traversal)
pub fn is_safe_path<P: AsRef<Path>>(path: P) -> bool {
    let path = path.as_ref();
    
    // Check for directory traversal attempts
    for component in path.components() {
        match component {
            std::path::Component::ParentDir => return false,
            std::path::Component::Normal(name) => {
                if name.to_str().map_or(true, |s| s.starts_with('.')) {
                    return false;
                }
            }
            _ => {}
        }
    }
    
    true
}

/// Get file extension
pub fn get_file_extension<P: AsRef<Path>>(path: P) -> Option<String> {
    path.as_ref()
        .extension()
        .and_then(|ext| ext.to_str())
        .map(|ext| ext.to_lowercase())
}

/// Check if file is binary
pub fn is_binary_file<P: AsRef<Path>>(path: P) -> Result<bool> {
    let mut file = File::open(path)?;
    let mut buffer = [0; 1024];
    let bytes_read = file.read(&mut buffer)?;
    
    // Check for null bytes which typically indicate binary content
    Ok(buffer[..bytes_read].contains(&0))
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;

    #[test]
    fn test_create_and_read_file() {
        let temp_dir = tempdir().unwrap();
        let file_path = temp_dir.path().join("test.txt");
        let content = "Hello, World!";
        
        let result = write_string_to_file(&file_path, content).unwrap();
        assert!(result.success);
        
        let read_content = read_file_to_string(&file_path).unwrap();
        assert_eq!(read_content, content);
    }

    #[test]
    fn test_file_metadata() {
        let temp_dir = tempdir().unwrap();
        let file_path = temp_dir.path().join("test.txt");
        write_string_to_file(&file_path, "test").unwrap();
        
        let metadata = get_file_metadata(&file_path).unwrap();
        assert!(metadata.is_file);
        assert!(!metadata.is_dir);
        assert_eq!(metadata.size, 4);
    }

    #[test]
    fn test_is_safe_path() {
        assert!(is_safe_path("safe/path/file.txt"));
        assert!(!is_safe_path("../unsafe/path"));
        assert!(!is_safe_path("safe/../unsafe"));
        assert!(!is_safe_path(".hidden/file"));
    }
}
