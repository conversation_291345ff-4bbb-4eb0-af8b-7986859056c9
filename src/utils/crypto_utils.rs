//! Cryptographic utilities for secure operations

use crate::error::Result;
use sha2::{Digest, Sha256, Sha512};
use ring::{digest, hmac, rand, signature};
use base64::{Engine as _, engine::general_purpose};
use serde::{Deserialize, Serialize};

/// Hash algorithms supported by the system
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum HashAlgorithm {
    Sha256,
    Sha512,
}

/// Digital signature algorithms
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum SignatureAlgorithm {
    Ed25519,
    EcdsaP256,
}

/// Cryptographic hash result
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct HashResult {
    pub algorithm: HashAlgorithm,
    pub hash: String,
    pub hex: String,
}

/// Digital signature result
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SignatureResult {
    pub algorithm: SignatureAlgorithm,
    pub signature: String,
    pub public_key: String,
}

/// Generate a cryptographic hash of the input data
pub fn hash_data(data: &[u8], algorithm: HashAlgorithm) -> Result<HashResult> {
    let (hash_bytes, algo) = match algorithm {
        HashAlgorithm::Sha256 => {
            let mut hasher = Sha256::new();
            hasher.update(data);
            (hasher.finalize().to_vec(), HashAlgorithm::Sha256)
        }
        HashAlgorithm::Sha512 => {
            let mut hasher = Sha512::new();
            hasher.update(data);
            (hasher.finalize().to_vec(), HashAlgorithm::Sha512)
        }
    };

    Ok(HashResult {
        algorithm: algo,
        hash: general_purpose::STANDARD.encode(&hash_bytes),
        hex: hex::encode(&hash_bytes),
    })
}

/// Generate a secure random string
pub fn generate_random_string(length: usize) -> Result<String> {
    let rng = rand::SystemRandom::new();
    let mut bytes = vec![0u8; length];
    rand::fill(&rng, &mut bytes)?;
    Ok(general_purpose::STANDARD.encode(&bytes))
}

/// Generate HMAC for message authentication
pub fn generate_hmac(key: &[u8], message: &[u8]) -> Result<String> {
    let key = hmac::Key::new(hmac::HMAC_SHA256, key);
    let signature = hmac::sign(&key, message);
    Ok(general_purpose::STANDARD.encode(signature.as_ref()))
}

/// Verify HMAC signature
pub fn verify_hmac(key: &[u8], message: &[u8], signature: &str) -> Result<bool> {
    let key = hmac::Key::new(hmac::HMAC_SHA256, key);
    let signature_bytes = general_purpose::STANDARD.decode(signature)?;
    
    match hmac::verify(&key, message, &signature_bytes) {
        Ok(()) => Ok(true),
        Err(_) => Ok(false),
    }
}

/// Generate Ed25519 key pair
pub fn generate_ed25519_keypair() -> Result<(Vec<u8>, Vec<u8>)> {
    let rng = rand::SystemRandom::new();
    let pkcs8_bytes = signature::Ed25519KeyPair::generate_pkcs8(&rng)?;
    let key_pair = signature::Ed25519KeyPair::from_pkcs8(pkcs8_bytes.as_ref())?;
    
    Ok((
        pkcs8_bytes.as_ref().to_vec(),
        key_pair.public_key().as_ref().to_vec(),
    ))
}

/// Sign data with Ed25519 private key
pub fn sign_ed25519(private_key: &[u8], data: &[u8]) -> Result<SignatureResult> {
    let key_pair = signature::Ed25519KeyPair::from_pkcs8(private_key)?;
    let signature = key_pair.sign(data);
    
    Ok(SignatureResult {
        algorithm: SignatureAlgorithm::Ed25519,
        signature: general_purpose::STANDARD.encode(signature.as_ref()),
        public_key: general_purpose::STANDARD.encode(key_pair.public_key().as_ref()),
    })
}

/// Verify Ed25519 signature
pub fn verify_ed25519(public_key: &[u8], data: &[u8], signature: &[u8]) -> Result<bool> {
    let public_key = signature::UnparsedPublicKey::new(&signature::ED25519, public_key);
    
    match public_key.verify(data, signature) {
        Ok(()) => Ok(true),
        Err(_) => Ok(false),
    }
}

/// Calculate file hash for integrity verification
pub fn calculate_file_hash(file_path: &std::path::Path) -> Result<HashResult> {
    let data = std::fs::read(file_path)?;
    hash_data(&data, HashAlgorithm::Sha256)
}

/// Generate secure API key
pub fn generate_api_key() -> Result<String> {
    generate_random_string(32)
}

/// Constant-time string comparison to prevent timing attacks
pub fn secure_compare(a: &str, b: &str) -> bool {
    if a.len() != b.len() {
        return false;
    }
    
    let mut result = 0u8;
    for (byte_a, byte_b) in a.bytes().zip(b.bytes()) {
        result |= byte_a ^ byte_b;
    }
    
    result == 0
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_hash_data() {
        let data = b"test data";
        let result = hash_data(data, HashAlgorithm::Sha256).unwrap();
        assert_eq!(result.algorithm, HashAlgorithm::Sha256);
        assert!(!result.hash.is_empty());
        assert!(!result.hex.is_empty());
    }

    #[test]
    fn test_generate_random_string() {
        let random1 = generate_random_string(16).unwrap();
        let random2 = generate_random_string(16).unwrap();
        assert_ne!(random1, random2);
        assert!(!random1.is_empty());
    }

    #[test]
    fn test_hmac_generation_and_verification() {
        let key = b"secret key";
        let message = b"test message";
        
        let signature = generate_hmac(key, message).unwrap();
        let is_valid = verify_hmac(key, message, &signature).unwrap();
        
        assert!(is_valid);
    }

    #[test]
    fn test_secure_compare() {
        assert!(secure_compare("hello", "hello"));
        assert!(!secure_compare("hello", "world"));
        assert!(!secure_compare("hello", "hello world"));
    }
}
