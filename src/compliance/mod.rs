//! # Compliance Module
//!
//! This module provides compliance framework support for various regulatory standards
//! including CERT-In, SEBI, RBI, IRDAI, HIPAA, ISO 27001, SOC 2, and GDPR.

pub mod cert_in_exporter;
pub mod cyclonedx_generator;
pub mod pdf_generator;
pub mod sebi_exporter;
pub mod spdx_generator;

use crate::{
    config::ComplianceConfig,
    error::{InfinitumError, Result},
    scanners::{ScanResult, SoftwareComponent, HardwareComponent},
};
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, path::Path};
use uuid::Uuid;

/// Re-export compliance types
pub use cert_in_exporter::{CertInExporter, CertInReport};
pub use cyclonedx_generator::{CycloneDxGenerator, CycloneDxBom};
pub use pdf_generator::{PdfGenerator, PdfReport};
pub use sebi_exporter::{SebiExporter, SebiReport};
pub use spdx_generator::{SpdxGenerator, SpdxDocument};

/// Supported compliance frameworks
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "kebab-case")]
pub enum ComplianceFramework {
    /// CERT-In (Indian Computer Emergency Response Team)
    CertIn,
    /// SEBI (Securities and Exchange Board of India)
    Sebi,
    /// RBI (Reserve Bank of India)
    Rbi,
    /// IRDAI (Insurance Regulatory and Development Authority of India)
    Irdai,
    /// HIPAA (Health Insurance Portability and Accountability Act)
    Hipaa,
    /// ISO 27001 Information Security Management
    Iso27001,
    /// SOC 2 (Service Organization Control 2)
    Soc2,
    /// GDPR (General Data Protection Regulation)
    Gdpr,
    /// NIST Cybersecurity Framework
    NistCsf,
    /// PCI DSS (Payment Card Industry Data Security Standard)
    PciDss,
}

/// Compliance report request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceRequest {
    /// Unique request identifier
    pub id: Uuid,
    /// Target compliance framework
    pub framework: ComplianceFramework,
    /// Scan results to include in report
    pub scan_results: Vec<ScanResult>,
    /// Report configuration
    pub config: ReportConfig,
    /// Additional metadata
    pub metadata: HashMap<String, String>,
}

/// Report configuration options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReportConfig {
    /// Report title
    pub title: String,
    /// Organization name
    pub organization: String,
    /// Report author
    pub author: String,
    /// Include executive summary
    pub include_executive_summary: bool,
    /// Include detailed findings
    pub include_detailed_findings: bool,
    /// Include remediation recommendations
    pub include_recommendations: bool,
    /// Include appendices
    pub include_appendices: bool,
    /// Output formats
    pub output_formats: Vec<OutputFormat>,
    /// Template customization
    pub template_options: HashMap<String, String>,
}

/// Output format options
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum OutputFormat {
    /// PDF report
    Pdf,
    /// HTML report
    Html,
    /// JSON data
    Json,
    /// XML data
    Xml,
    /// CSV data
    Csv,
    /// CycloneDX SBOM
    CycloneDx,
    /// SPDX SBOM
    Spdx,
}

/// Compliance report result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceReport {
    /// Report request
    pub request: ComplianceRequest,
    /// Report status
    pub status: ReportStatus,
    /// Generated at timestamp
    pub generated_at: chrono::DateTime<chrono::Utc>,
    /// Report summary
    pub summary: ReportSummary,
    /// Compliance findings
    pub findings: Vec<ComplianceFinding>,
    /// Risk assessment
    pub risk_assessment: RiskAssessment,
    /// Recommendations
    pub recommendations: Vec<Recommendation>,
    /// Generated file paths
    pub output_files: HashMap<OutputFormat, String>,
    /// Report metadata
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Report generation status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ReportStatus {
    /// Report generation in progress
    InProgress,
    /// Report generated successfully
    Completed,
    /// Report generation failed
    Failed,
    /// Report generation cancelled
    Cancelled,
}

/// Report summary information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReportSummary {
    /// Total components analyzed
    pub total_components: u32,
    /// Total vulnerabilities found
    pub total_vulnerabilities: u32,
    /// High severity issues
    pub high_severity_issues: u32,
    /// Medium severity issues
    pub medium_severity_issues: u32,
    /// Low severity issues
    pub low_severity_issues: u32,
    /// Compliance score (0-100)
    pub compliance_score: f64,
    /// Overall risk level
    pub risk_level: RiskLevel,
}

/// Compliance finding
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceFinding {
    /// Finding identifier
    pub id: String,
    /// Finding title
    pub title: String,
    /// Finding description
    pub description: String,
    /// Severity level
    pub severity: Severity,
    /// Compliance control reference
    pub control_reference: String,
    /// Affected components
    pub affected_components: Vec<String>,
    /// Evidence
    pub evidence: Vec<String>,
    /// Status
    pub status: FindingStatus,
}

/// Risk assessment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskAssessment {
    /// Overall risk score (0-100)
    pub overall_risk_score: f64,
    /// Risk level
    pub risk_level: RiskLevel,
    /// Risk factors
    pub risk_factors: Vec<RiskFactor>,
    /// Mitigation strategies
    pub mitigation_strategies: Vec<String>,
}

/// Risk factor
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RiskFactor {
    /// Factor name
    pub name: String,
    /// Factor description
    pub description: String,
    /// Impact score (0-10)
    pub impact_score: f64,
    /// Likelihood score (0-10)
    pub likelihood_score: f64,
    /// Risk score (impact * likelihood)
    pub risk_score: f64,
}

/// Recommendation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Recommendation {
    /// Recommendation identifier
    pub id: String,
    /// Recommendation title
    pub title: String,
    /// Recommendation description
    pub description: String,
    /// Priority level
    pub priority: Priority,
    /// Implementation effort
    pub effort: ImplementationEffort,
    /// Expected impact
    pub expected_impact: String,
    /// Implementation steps
    pub implementation_steps: Vec<String>,
}

/// Severity levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
#[serde(rename_all = "lowercase")]
pub enum Severity {
    /// Informational
    Info,
    /// Low severity
    Low,
    /// Medium severity
    Medium,
    /// High severity
    High,
    /// Critical severity
    Critical,
}

/// Risk levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum RiskLevel {
    /// Very low risk
    VeryLow,
    /// Low risk
    Low,
    /// Medium risk
    Medium,
    /// High risk
    High,
    /// Very high risk
    VeryHigh,
}

/// Finding status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum FindingStatus {
    /// Open finding
    Open,
    /// In progress
    InProgress,
    /// Resolved
    Resolved,
    /// Accepted risk
    Accepted,
    /// False positive
    FalsePositive,
}

/// Priority levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
#[serde(rename_all = "lowercase")]
pub enum Priority {
    /// Low priority
    Low,
    /// Medium priority
    Medium,
    /// High priority
    High,
    /// Critical priority
    Critical,
}

/// Implementation effort levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum ImplementationEffort {
    /// Low effort (< 1 day)
    Low,
    /// Medium effort (1-5 days)
    Medium,
    /// High effort (1-4 weeks)
    High,
    /// Very high effort (> 1 month)
    VeryHigh,
}

/// Main compliance orchestrator
pub struct ComplianceOrchestrator {
    config: ComplianceConfig,
    cert_in_exporter: CertInExporter,
    sebi_exporter: SebiExporter,
    cyclonedx_generator: CycloneDxGenerator,
    spdx_generator: SpdxGenerator,
    pdf_generator: PdfGenerator,
}

impl ComplianceOrchestrator {
    /// Create new compliance orchestrator
    pub fn new(config: ComplianceConfig) -> Self {
        Self {
            cert_in_exporter: CertInExporter::new(&config),
            sebi_exporter: SebiExporter::new(&config),
            cyclonedx_generator: CycloneDxGenerator::new(&config),
            spdx_generator: SpdxGenerator::new(&config),
            pdf_generator: PdfGenerator::new(&config),
            config,
        }
    }

    /// Generate compliance report
    pub async fn generate_report(&self, request: ComplianceRequest) -> Result<ComplianceReport> {
        let mut report = ComplianceReport {
            request: request.clone(),
            status: ReportStatus::InProgress,
            generated_at: chrono::Utc::now(),
            summary: ReportSummary {
                total_components: 0,
                total_vulnerabilities: 0,
                high_severity_issues: 0,
                medium_severity_issues: 0,
                low_severity_issues: 0,
                compliance_score: 0.0,
                risk_level: RiskLevel::Medium,
            },
            findings: Vec::new(),
            risk_assessment: RiskAssessment {
                overall_risk_score: 0.0,
                risk_level: RiskLevel::Medium,
                risk_factors: Vec::new(),
                mitigation_strategies: Vec::new(),
            },
            recommendations: Vec::new(),
            output_files: HashMap::new(),
            metadata: HashMap::new(),
        };

        // Generate report based on framework
        match request.framework {
            ComplianceFramework::CertIn => {
                self.generate_cert_in_report(&request, &mut report).await?;
            }
            ComplianceFramework::Sebi => {
                self.generate_sebi_report(&request, &mut report).await?;
            }
            ComplianceFramework::Iso27001 => {
                self.generate_iso27001_report(&request, &mut report).await?;
            }
            _ => {
                return Err(InfinitumError::UnsupportedFramework {
                    framework: format!("{:?}", request.framework),
                });
            }
        }

        // Generate output files
        self.generate_output_files(&request, &mut report).await?;

        report.status = ReportStatus::Completed;
        Ok(report)
    }

    /// Generate CERT-In compliance report
    async fn generate_cert_in_report(
        &self,
        request: &ComplianceRequest,
        report: &mut ComplianceReport,
    ) -> Result<()> {
        let cert_in_report = self.cert_in_exporter.generate_report(&request.scan_results).await?;
        
        // Convert CERT-In specific findings to generic compliance findings
        for finding in cert_in_report.findings {
            report.findings.push(ComplianceFinding {
                id: finding.id,
                title: finding.title,
                description: finding.description,
                severity: finding.severity,
                control_reference: finding.control_reference,
                affected_components: finding.affected_components,
                evidence: finding.evidence,
                status: FindingStatus::Open,
            });
        }

        Ok(())
    }

    /// Generate SEBI compliance report
    async fn generate_sebi_report(
        &self,
        request: &ComplianceRequest,
        report: &mut ComplianceReport,
    ) -> Result<()> {
        let sebi_report = self.sebi_exporter.generate_report(&request.scan_results).await?;
        
        // Convert SEBI specific findings to generic compliance findings
        for finding in sebi_report.findings {
            report.findings.push(ComplianceFinding {
                id: finding.id,
                title: finding.title,
                description: finding.description,
                severity: finding.severity,
                control_reference: finding.control_reference,
                affected_components: finding.affected_components,
                evidence: finding.evidence,
                status: FindingStatus::Open,
            });
        }

        Ok(())
    }

    /// Generate ISO 27001 compliance report
    async fn generate_iso27001_report(
        &self,
        _request: &ComplianceRequest,
        _report: &mut ComplianceReport,
    ) -> Result<()> {
        // TODO: Implement ISO 27001 compliance report generation
        Ok(())
    }

    /// Generate output files in requested formats
    async fn generate_output_files(
        &self,
        request: &ComplianceRequest,
        report: &mut ComplianceReport,
    ) -> Result<()> {
        for format in &request.config.output_formats {
            match format {
                OutputFormat::Pdf => {
                    let pdf_path = self.pdf_generator.generate_pdf_report(report).await?;
                    report.output_files.insert(OutputFormat::Pdf, pdf_path);
                }
                OutputFormat::Json => {
                    let json_path = self.generate_json_report(report).await?;
                    report.output_files.insert(OutputFormat::Json, json_path);
                }
                OutputFormat::CycloneDx => {
                    let cyclonedx_path = self.cyclonedx_generator.generate_bom(&request.scan_results).await?;
                    report.output_files.insert(OutputFormat::CycloneDx, cyclonedx_path);
                }
                OutputFormat::Spdx => {
                    let spdx_path = self.spdx_generator.generate_document(&request.scan_results).await?;
                    report.output_files.insert(OutputFormat::Spdx, spdx_path);
                }
                _ => {
                    // TODO: Implement other output formats
                }
            }
        }

        Ok(())
    }

    /// Generate JSON report
    async fn generate_json_report(&self, report: &ComplianceReport) -> Result<String> {
        let output_dir = &self.config.report_output_dir;
        let filename = format!("compliance_report_{}.json", report.request.id);
        let file_path = Path::new(output_dir).join(&filename);

        let json_content = serde_json::to_string_pretty(report)?;
        tokio::fs::write(&file_path, json_content).await?;

        Ok(file_path.to_string_lossy().to_string())
    }
}

impl Default for ReportConfig {
    fn default() -> Self {
        Self {
            title: "Compliance Report".to_string(),
            organization: "Organization".to_string(),
            author: "Infinitium Signal".to_string(),
            include_executive_summary: true,
            include_detailed_findings: true,
            include_recommendations: true,
            include_appendices: true,
            output_formats: vec![OutputFormat::Pdf, OutputFormat::Json],
            template_options: HashMap::new(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_compliance_framework_serialization() {
        let framework = ComplianceFramework::CertIn;
        let serialized = serde_json::to_string(&framework).unwrap();
        assert_eq!(serialized, "\"cert-in\"");
    }

    #[test]
    fn test_severity_ordering() {
        assert!(Severity::Critical > Severity::High);
        assert!(Severity::High > Severity::Medium);
        assert!(Severity::Medium > Severity::Low);
        assert!(Severity::Low > Severity::Info);
    }

    #[test]
    fn test_report_config_default() {
        let config = ReportConfig::default();
        assert_eq!(config.title, "Compliance Report");
        assert!(config.include_executive_summary);
        assert_eq!(config.output_formats.len(), 2);
    }
}
