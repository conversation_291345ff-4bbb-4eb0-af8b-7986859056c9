use crate::{config::ComplianceConfig, error::Result, compliance::ComplianceReport};
use serde::{Deserialize, Serialize};
use std::process::Command;
use tracing::{info, instrument, warn};
use uuid::Uuid;

/// PDF report generator using wkhtmltopdf
pub struct PdfGenerator {
    config: ComplianceConfig,
}

/// PDF report configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PdfReport {
    /// Report title
    pub title: String,
    /// HTML content
    pub html_content: String,
    /// CSS styling
    pub css_content: String,
    /// Output file path
    pub output_path: String,
}

impl PdfGenerator {
    /// Create new PDF generator
    pub fn new(config: &ComplianceConfig) -> Self {
        Self { config: config.clone() }
    }

    /// Generate PDF report from compliance report
    #[instrument(skip(self, report))]
    pub async fn generate_pdf_report(&self, report: &ComplianceReport) -> Result<String> {
        info!("Generating PDF compliance report");

        // Generate HTML content
        let html_content = self.generate_html_content(report).await?;
        
        // Generate CSS styling
        let css_content = self.generate_css_content().await?;

        // Create temporary HTML file
        let temp_html_path = self.create_temp_html_file(&html_content, &css_content).await?;

        // Generate PDF using wkhtmltopdf
        let output_dir = &self.config.report_output_dir;
        let filename = format!("compliance_report_{}.pdf", report.request.id);
        let pdf_path = std::path::Path::new(output_dir).join(&filename);

        tokio::fs::create_dir_all(output_dir).await?;

        self.convert_html_to_pdf(&temp_html_path, &pdf_path).await?;

        // Clean up temporary file
        let _ = tokio::fs::remove_file(&temp_html_path).await;

        info!(pdf_path = %pdf_path.display(), "PDF report generated");
        Ok(pdf_path.to_string_lossy().to_string())
    }

    /// Generate HTML content for the report
    async fn generate_html_content(&self, report: &ComplianceReport) -> Result<String> {
        let html = format!(r#"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{}</title>
    <style>
        {}
    </style>
</head>
<body>
    <div class="header">
        <h1>{}</h1>
        <div class="metadata">
            <p><strong>Framework:</strong> {:?}</p>
            <p><strong>Generated:</strong> {}</p>
            <p><strong>Organization:</strong> {}</p>
        </div>
    </div>

    <div class="executive-summary">
        <h2>Executive Summary</h2>
        <div class="summary-stats">
            <div class="stat-box">
                <h3>Compliance Score</h3>
                <div class="score">{:.1}%</div>
            </div>
            <div class="stat-box">
                <h3>Total Components</h3>
                <div class="count">{}</div>
            </div>
            <div class="stat-box">
                <h3>Vulnerabilities</h3>
                <div class="count">{}</div>
            </div>
            <div class="stat-box">
                <h3>Risk Level</h3>
                <div class="risk-level {:?}">{:?}</div>
            </div>
        </div>
    </div>

    <div class="findings-section">
        <h2>Compliance Findings</h2>
        {}
    </div>

    <div class="recommendations-section">
        <h2>Recommendations</h2>
        {}
    </div>

    <div class="footer">
        <p>Generated by Infinitium Signal - Enterprise Cyber-Compliance Platform</p>
        <p>Report ID: {}</p>
    </div>
</body>
</html>
        "#,
            report.request.config.title,
            self.generate_css_content().await?,
            report.request.config.title,
            report.request.framework,
            report.generated_at.format("%Y-%m-%d %H:%M:%S UTC"),
            report.request.config.organization,
            report.summary.compliance_score,
            report.summary.total_components,
            report.summary.total_vulnerabilities,
            report.summary.risk_level,
            report.summary.risk_level,
            self.generate_findings_html(&report.findings),
            self.generate_recommendations_html(&report.recommendations),
            report.request.id
        );

        Ok(html)
    }

    /// Generate CSS content for styling
    async fn generate_css_content(&self) -> Result<String> {
        Ok(r#"
body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 20px;
    color: #333;
}

.header {
    border-bottom: 3px solid #2c3e50;
    padding-bottom: 20px;
    margin-bottom: 30px;
}

.header h1 {
    color: #2c3e50;
    margin: 0;
    font-size: 2.5em;
}

.metadata {
    margin-top: 15px;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
}

.metadata p {
    margin: 5px 0;
}

.executive-summary {
    margin-bottom: 40px;
}

.summary-stats {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.stat-box {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    flex: 1;
    margin: 0 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-box h3 {
    margin: 0 0 10px 0;
    color: #666;
    font-size: 0.9em;
    text-transform: uppercase;
}

.score {
    font-size: 2.5em;
    font-weight: bold;
    color: #27ae60;
}

.count {
    font-size: 2em;
    font-weight: bold;
    color: #3498db;
}

.risk-level {
    font-size: 1.5em;
    font-weight: bold;
    padding: 5px 10px;
    border-radius: 5px;
}

.risk-level.Low {
    background: #d4edda;
    color: #155724;
}

.risk-level.Medium {
    background: #fff3cd;
    color: #856404;
}

.risk-level.High {
    background: #f8d7da;
    color: #721c24;
}

.risk-level.VeryHigh {
    background: #721c24;
    color: white;
}

.findings-section, .recommendations-section {
    margin-bottom: 40px;
}

.finding {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.finding-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.finding-title {
    font-size: 1.2em;
    font-weight: bold;
    color: #2c3e50;
}

.severity {
    padding: 5px 10px;
    border-radius: 5px;
    color: white;
    font-weight: bold;
    text-transform: uppercase;
    font-size: 0.8em;
}

.severity.Critical {
    background: #e74c3c;
}

.severity.High {
    background: #e67e22;
}

.severity.Medium {
    background: #f39c12;
}

.severity.Low {
    background: #27ae60;
}

.severity.Info {
    background: #3498db;
}

.recommendation {
    background: #e8f5e8;
    border-left: 4px solid #27ae60;
    padding: 15px;
    margin-bottom: 15px;
}

.recommendation-title {
    font-weight: bold;
    color: #27ae60;
    margin-bottom: 10px;
}

.footer {
    border-top: 2px solid #2c3e50;
    padding-top: 20px;
    margin-top: 40px;
    text-align: center;
    color: #666;
    font-size: 0.9em;
}

@media print {
    body {
        padding: 0;
    }
    
    .stat-box {
        margin: 0 5px;
    }
    
    .finding {
        page-break-inside: avoid;
    }
}
        "#.to_string())
    }

    /// Generate HTML for findings
    fn generate_findings_html(&self, findings: &[crate::compliance::ComplianceFinding]) -> String {
        if findings.is_empty() {
            return "<p>No compliance findings identified.</p>".to_string();
        }

        findings.iter().map(|finding| {
            format!(r#"
<div class="finding">
    <div class="finding-header">
        <div class="finding-title">{}</div>
        <div class="severity {:?}">{:?}</div>
    </div>
    <p><strong>Control Reference:</strong> {}</p>
    <p><strong>Description:</strong> {}</p>
    <p><strong>Affected Components:</strong> {}</p>
    {}
</div>
            "#,
                finding.title,
                finding.severity,
                finding.severity,
                finding.control_reference,
                finding.description,
                finding.affected_components.join(", "),
                if !finding.evidence.is_empty() {
                    format!("<p><strong>Evidence:</strong></p><ul>{}</ul>", 
                        finding.evidence.iter().map(|e| format!("<li>{}</li>", e)).collect::<String>())
                } else {
                    String::new()
                }
            )
        }).collect()
    }

    /// Generate HTML for recommendations
    fn generate_recommendations_html(&self, recommendations: &[crate::compliance::Recommendation]) -> String {
        if recommendations.is_empty() {
            return "<p>No specific recommendations at this time.</p>".to_string();
        }

        recommendations.iter().map(|rec| {
            format!(r#"
<div class="recommendation">
    <div class="recommendation-title">{}</div>
    <p>{}</p>
    <p><strong>Priority:</strong> {:?} | <strong>Effort:</strong> {:?}</p>
    {}
</div>
            "#,
                rec.title,
                rec.description,
                rec.priority,
                rec.effort,
                if !rec.implementation_steps.is_empty() {
                    format!("<p><strong>Implementation Steps:</strong></p><ol>{}</ol>", 
                        rec.implementation_steps.iter().map(|s| format!("<li>{}</li>", s)).collect::<String>())
                } else {
                    String::new()
                }
            )
        }).collect()
    }

    /// Create temporary HTML file
    async fn create_temp_html_file(&self, html_content: &str, _css_content: &str) -> Result<String> {
        let temp_dir = std::env::temp_dir();
        let temp_file = temp_dir.join(format!("infinitum_report_{}.html", Uuid::new_v4()));
        
        tokio::fs::write(&temp_file, html_content).await?;
        
        Ok(temp_file.to_string_lossy().to_string())
    }

    /// Convert HTML to PDF using wkhtmltopdf
    async fn convert_html_to_pdf(&self, html_path: &str, pdf_path: &std::path::Path) -> Result<()> {
        // Check if wkhtmltopdf is available
        let output = Command::new("wkhtmltopdf")
            .arg("--version")
            .output();

        if output.is_err() {
            warn!("wkhtmltopdf not found, attempting to install");
            self.install_wkhtmltopdf().await?;
        }

        // Convert HTML to PDF
        let output = Command::new("wkhtmltopdf")
            .args([
                "--page-size", "A4",
                "--orientation", "Portrait",
                "--margin-top", "0.75in",
                "--margin-right", "0.75in",
                "--margin-bottom", "0.75in",
                "--margin-left", "0.75in",
                "--encoding", "UTF-8",
                "--print-media-type",
                "--disable-smart-shrinking",
                html_path,
                &pdf_path.to_string_lossy(),
            ])
            .output()?;

        if !output.status.success() {
            let error_msg = String::from_utf8_lossy(&output.stderr);
            return Err(crate::error::InfinitumError::ReportGeneration {
                message: format!("wkhtmltopdf failed: {}", error_msg),
            });
        }

        Ok(())
    }

    /// Install wkhtmltopdf if not available
    async fn install_wkhtmltopdf(&self) -> Result<()> {
        info!("Installing wkhtmltopdf");

        // Try different package managers
        let install_commands = [
            ("apt-get", vec!["update", "&&", "apt-get", "install", "-y", "wkhtmltopdf"]),
            ("yum", vec!["install", "-y", "wkhtmltopdf"]),
            ("dnf", vec!["install", "-y", "wkhtmltopdf"]),
            ("pacman", vec!["-S", "--noconfirm", "wkhtmltopdf"]),
            ("brew", vec!["install", "wkhtmltopdf"]),
        ];

        for (cmd, args) in &install_commands {
            let output = Command::new(cmd)
                .args(args)
                .output();

            if let Ok(result) = output {
                if result.status.success() {
                    info!("Successfully installed wkhtmltopdf using {}", cmd);
                    return Ok(());
                }
            }
        }

        Err(crate::error::InfinitumError::ReportGeneration {
            message: "Failed to install wkhtmltopdf. Please install it manually.".to_string(),
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_pdf_generator_creation() {
        let config = ComplianceConfig::default();
        let generator = PdfGenerator::new(&config);
        assert_eq!(generator.config.frameworks.len(), config.frameworks.len());
    }

    #[tokio::test]
    async fn test_css_generation() {
        let config = ComplianceConfig::default();
        let generator = PdfGenerator::new(&config);
        let css = generator.generate_css_content().await.unwrap();
        assert!(css.contains("body"));
        assert!(css.contains("font-family"));
    }
}
