use crate::{
    config::VulnerabilityConfig,
    error::{InfinitumError, Result},
    scanners::SoftwareComponent,
    vulnerability::{Vulnerability, VulnerabilitySeverity, VulnerabilitySource, CvssScore, CvssVersion},
};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::time::{sleep, Duration};
use tracing::{debug, info, instrument, warn};

/// NVD (National Vulnerability Database) client
pub struct NvdClient {
    client: Client,
    api_key: Option<String>,
    base_url: String,
    rate_limit_delay: Duration,
}

/// NVD API response for CVE data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NvdResponse {
    #[serde(rename = "resultsPerPage")]
    pub results_per_page: u32,
    #[serde(rename = "startIndex")]
    pub start_index: u32,
    #[serde(rename = "totalResults")]
    pub total_results: u32,
    pub format: String,
    pub version: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub vulnerabilities: Vec<NvdVulnerability>,
}

/// NVD vulnerability data structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NvdVulnerability {
    pub cve: NvdCve,
}

/// NVD CVE data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NvdCve {
    pub id: String,
    #[serde(rename = "sourceIdentifier")]
    pub source_identifier: Option<String>,
    pub published: chrono::DateTime<chrono::Utc>,
    #[serde(rename = "lastModified")]
    pub last_modified: chrono::DateTime<chrono::Utc>,
    #[serde(rename = "vulnStatus")]
    pub vuln_status: String,
    pub descriptions: Vec<NvdDescription>,
    pub metrics: Option<NvdMetrics>,
    pub weaknesses: Option<Vec<NvdWeakness>>,
    pub configurations: Option<Vec<NvdConfiguration>>,
    pub references: Option<Vec<NvdReference>>,
}

/// NVD description
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NvdDescription {
    pub lang: String,
    pub value: String,
}

/// NVD metrics (CVSS scores)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NvdMetrics {
    #[serde(rename = "cvssMetricV31")]
    pub cvss_metric_v31: Option<Vec<NvdCvssV31>>,
    #[serde(rename = "cvssMetricV30")]
    pub cvss_metric_v30: Option<Vec<NvdCvssV30>>,
    #[serde(rename = "cvssMetricV2")]
    pub cvss_metric_v2: Option<Vec<NvdCvssV2>>,
}

/// CVSS v3.1 metric
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NvdCvssV31 {
    pub source: String,
    pub type_field: String,
    #[serde(rename = "cvssData")]
    pub cvss_data: NvdCvssV31Data,
    #[serde(rename = "exploitabilityScore")]
    pub exploitability_score: Option<f64>,
    #[serde(rename = "impactScore")]
    pub impact_score: Option<f64>,
}

/// CVSS v3.1 data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NvdCvssV31Data {
    pub version: String,
    #[serde(rename = "vectorString")]
    pub vector_string: String,
    #[serde(rename = "attackVector")]
    pub attack_vector: String,
    #[serde(rename = "attackComplexity")]
    pub attack_complexity: String,
    #[serde(rename = "privilegesRequired")]
    pub privileges_required: String,
    #[serde(rename = "userInteraction")]
    pub user_interaction: String,
    pub scope: String,
    #[serde(rename = "confidentialityImpact")]
    pub confidentiality_impact: String,
    #[serde(rename = "integrityImpact")]
    pub integrity_impact: String,
    #[serde(rename = "availabilityImpact")]
    pub availability_impact: String,
    #[serde(rename = "baseScore")]
    pub base_score: f64,
    #[serde(rename = "baseSeverity")]
    pub base_severity: String,
}

/// CVSS v3.0 metric (similar structure to v3.1)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NvdCvssV30 {
    pub source: String,
    #[serde(rename = "type")]
    pub type_field: String,
    #[serde(rename = "cvssData")]
    pub cvss_data: NvdCvssV31Data, // Same structure as v3.1
}

/// CVSS v2 metric
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NvdCvssV2 {
    pub source: String,
    #[serde(rename = "type")]
    pub type_field: String,
    #[serde(rename = "cvssData")]
    pub cvss_data: NvdCvssV2Data,
    #[serde(rename = "baseSeverity")]
    pub base_severity: Option<String>,
    #[serde(rename = "exploitabilityScore")]
    pub exploitability_score: Option<f64>,
    #[serde(rename = "impactScore")]
    pub impact_score: Option<f64>,
}

/// CVSS v2 data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NvdCvssV2Data {
    pub version: String,
    #[serde(rename = "vectorString")]
    pub vector_string: String,
    #[serde(rename = "accessVector")]
    pub access_vector: String,
    #[serde(rename = "accessComplexity")]
    pub access_complexity: String,
    pub authentication: String,
    #[serde(rename = "confidentialityImpact")]
    pub confidentiality_impact: String,
    #[serde(rename = "integrityImpact")]
    pub integrity_impact: String,
    #[serde(rename = "availabilityImpact")]
    pub availability_impact: String,
    #[serde(rename = "baseScore")]
    pub base_score: f64,
}

/// NVD weakness (CWE)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NvdWeakness {
    pub source: String,
    #[serde(rename = "type")]
    pub type_field: String,
    pub description: Vec<NvdDescription>,
}

/// NVD configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NvdConfiguration {
    pub nodes: Vec<NvdNode>,
}

/// NVD configuration node
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NvdNode {
    pub operator: String,
    pub negate: Option<bool>,
    #[serde(rename = "cpeMatch")]
    pub cpe_match: Option<Vec<NvdCpeMatch>>,
}

/// NVD CPE match
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NvdCpeMatch {
    pub vulnerable: bool,
    pub criteria: String,
    #[serde(rename = "versionStartIncluding")]
    pub version_start_including: Option<String>,
    #[serde(rename = "versionStartExcluding")]
    pub version_start_excluding: Option<String>,
    #[serde(rename = "versionEndIncluding")]
    pub version_end_including: Option<String>,
    #[serde(rename = "versionEndExcluding")]
    pub version_end_excluding: Option<String>,
}

/// NVD reference
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NvdReference {
    pub url: String,
    pub source: Option<String>,
    pub tags: Option<Vec<String>>,
}

impl NvdClient {
    /// Create new NVD client
    pub fn new(config: &VulnerabilityConfig) -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .user_agent("infinitum-signal/0.1.0")
            .build()
            .expect("Failed to create HTTP client");

        Self {
            client,
            api_key: std::env::var("NVD_API_KEY").ok(),
            base_url: "https://services.nvd.nist.gov/rest/json/cves/2.0".to_string(),
            rate_limit_delay: Duration::from_millis(6000), // NVD rate limit: 10 requests per minute without API key
        }
    }

    /// Query vulnerabilities for given components
    #[instrument(skip(self, components))]
    pub async fn query_vulnerabilities(&self, components: &[SoftwareComponent]) -> Result<Vec<Vulnerability>> {
        info!("Querying NVD for {} components", components.len());

        let mut vulnerabilities = Vec::new();

        for component in components {
            debug!("Querying NVD for component: {}", component.name);
            
            // Search by keyword (component name)
            let keyword_vulns = self.search_by_keyword(&component.name).await?;
            vulnerabilities.extend(keyword_vulns);

            // Rate limiting
            sleep(self.rate_limit_delay).await;
        }

        info!("Found {} vulnerabilities from NVD", vulnerabilities.len());
        Ok(vulnerabilities)
    }

    /// Search vulnerabilities by keyword
    async fn search_by_keyword(&self, keyword: &str) -> Result<Vec<Vulnerability>> {
        let mut url = format!("{}?keywordSearch={}", self.base_url, urlencoding::encode(keyword));
        
        // Add API key if available
        if let Some(api_key) = &self.api_key {
            url.push_str(&format!("&apiKey={}", api_key));
        }

        let mut request = self.client.get(&url);
        
        // Add headers
        request = request.header("Accept", "application/json");

        let response = request.send().await?;

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await.unwrap_or_default();
            return Err(InfinitumError::NvdApi {
                message: format!("NVD API error {}: {}", status, error_text),
            });
        }

        let nvd_response: NvdResponse = response.json().await?;
        
        let vulnerabilities = nvd_response.vulnerabilities
            .into_iter()
            .map(|nvd_vuln| self.convert_nvd_vulnerability(nvd_vuln))
            .collect();

        Ok(vulnerabilities)
    }

    /// Convert NVD vulnerability to internal format
    fn convert_nvd_vulnerability(&self, nvd_vuln: NvdVulnerability) -> Vulnerability {
        let cve = nvd_vuln.cve;
        
        // Extract description
        let description = cve.descriptions
            .iter()
            .find(|d| d.lang == "en")
            .map(|d| d.value.clone())
            .unwrap_or_else(|| "No description available".to_string());

        // Extract CVSS scores
        let mut cvss_scores = Vec::new();
        if let Some(metrics) = &cve.metrics {
            // CVSS v3.1
            if let Some(cvss_v31) = &metrics.cvss_metric_v31 {
                for metric in cvss_v31 {
                    cvss_scores.push(CvssScore {
                        version: CvssVersion::V31,
                        base_score: metric.cvss_data.base_score,
                        temporal_score: None,
                        environmental_score: None,
                        vector_string: metric.cvss_data.vector_string.clone(),
                        severity: self.map_severity(&metric.cvss_data.base_severity),
                    });
                }
            }

            // CVSS v3.0
            if let Some(cvss_v30) = &metrics.cvss_metric_v30 {
                for metric in cvss_v30 {
                    cvss_scores.push(CvssScore {
                        version: CvssVersion::V3,
                        base_score: metric.cvss_data.base_score,
                        temporal_score: None,
                        environmental_score: None,
                        vector_string: metric.cvss_data.vector_string.clone(),
                        severity: self.map_severity(&metric.cvss_data.base_severity),
                    });
                }
            }

            // CVSS v2
            if let Some(cvss_v2) = &metrics.cvss_metric_v2 {
                for metric in cvss_v2 {
                    cvss_scores.push(CvssScore {
                        version: CvssVersion::V2,
                        base_score: metric.cvss_data.base_score,
                        temporal_score: None,
                        environmental_score: None,
                        vector_string: metric.cvss_data.vector_string.clone(),
                        severity: metric.base_severity.as_ref()
                            .map(|s| self.map_severity(s))
                            .unwrap_or(VulnerabilitySeverity::Unknown),
                    });
                }
            }
        }

        // Determine overall severity
        let severity = cvss_scores.iter()
            .map(|score| &score.severity)
            .max()
            .cloned()
            .unwrap_or(VulnerabilitySeverity::Unknown);

        // Extract CWEs
        let cwes = cve.weaknesses
            .unwrap_or_default()
            .into_iter()
            .flat_map(|weakness| {
                weakness.description.into_iter()
                    .filter(|desc| desc.lang == "en")
                    .map(|desc| desc.value)
            })
            .collect();

        // Extract references
        let references = cve.references
            .unwrap_or_default()
            .into_iter()
            .map(|ref_item| crate::vulnerability::VulnerabilityReference {
                reference_type: crate::vulnerability::ReferenceType::Web,
                url: ref_item.url,
            })
            .collect();

        Vulnerability {
            id: cve.id,
            aliases: Vec::new(),
            summary: description.clone(),
            details: Some(description),
            severity,
            cvss_scores,
            epss_score: None,
            cwes,
            affected: Vec::new(), // TODO: Parse from configurations
            references,
            published: cve.published,
            modified: cve.last_modified,
            withdrawn: None,
            source: VulnerabilitySource::Nvd,
            exploits: Vec::new(),
            patches: Vec::new(),
        }
    }

    /// Map NVD severity string to internal severity enum
    fn map_severity(&self, severity: &str) -> VulnerabilitySeverity {
        match severity.to_uppercase().as_str() {
            "CRITICAL" => VulnerabilitySeverity::Critical,
            "HIGH" => VulnerabilitySeverity::High,
            "MEDIUM" => VulnerabilitySeverity::Medium,
            "LOW" => VulnerabilitySeverity::Low,
            _ => VulnerabilitySeverity::Unknown,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::VulnerabilityConfig;

    #[test]
    fn test_nvd_client_creation() {
        let config = VulnerabilityConfig::default();
        let client = NvdClient::new(&config);
        assert_eq!(client.base_url, "https://services.nvd.nist.gov/rest/json/cves/2.0");
    }

    #[test]
    fn test_severity_mapping() {
        let config = VulnerabilityConfig::default();
        let client = NvdClient::new(&config);
        
        assert_eq!(client.map_severity("CRITICAL"), VulnerabilitySeverity::Critical);
        assert_eq!(client.map_severity("HIGH"), VulnerabilitySeverity::High);
        assert_eq!(client.map_severity("MEDIUM"), VulnerabilitySeverity::Medium);
        assert_eq!(client.map_severity("LOW"), VulnerabilitySeverity::Low);
        assert_eq!(client.map_severity("UNKNOWN"), VulnerabilitySeverity::Unknown);
    }

    #[tokio::test]
    async fn test_nvd_response_deserialization() {
        let json_data = r#"
        {
            "resultsPerPage": 20,
            "startIndex": 0,
            "totalResults": 1,
            "format": "NVD_CVE",
            "version": "2.0",
            "timestamp": "2024-01-01T00:00:00.000Z",
            "vulnerabilities": []
        }
        "#;

        let response: NvdResponse = serde_json::from_str(json_data).unwrap();
        assert_eq!(response.results_per_page, 20);
        assert_eq!(response.total_results, 1);
    }
}
