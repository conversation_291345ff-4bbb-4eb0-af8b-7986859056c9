use crate::{
    config::VulnerabilityConfig,
    error::Result,
    scanners::SoftwareComponent,
    vulnerability::{Vulnerability, VulnerabilitySeverity},
};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use tracing::{debug, info, instrument};

/// CVE matcher for correlating vulnerabilities with software components
pub struct CveMatcher {
    config: VulnerabilityConfig,
    version_patterns: HashMap<String, Vec<VersionPattern>>,
}

/// CVE match result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CveMatch {
    /// Component that matches
    pub component: SoftwareComponent,
    /// Matching vulnerability
    pub vulnerability: Vulnerability,
    /// Match confidence (0.0 - 1.0)
    pub confidence: f64,
    /// Match type
    pub match_type: MatchType,
    /// Match details
    pub match_details: MatchDetails,
}

/// Types of matches
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum MatchType {
    /// Exact name and version match
    ExactMatch,
    /// Name matches, version is in affected range
    VersionRangeMatch,
    /// Name matches, but version is unclear
    NameMatch,
    /// Fuzzy name match (similar names)
    FuzzyMatch,
    /// CPE (Common Platform Enumeration) match
    CpeMatch,
    /// PURL (Package URL) match
    PurlMatch,
}

/// Match details
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MatchDetails {
    /// Matched component name
    pub matched_name: String,
    /// Matched version range
    pub matched_version_range: Option<String>,
    /// CPE string if applicable
    pub cpe: Option<String>,
    /// PURL string if applicable
    pub purl: Option<String>,
    /// Additional metadata
    pub metadata: HashMap<String, String>,
}

/// Version pattern for matching
#[derive(Debug, Clone)]
pub struct VersionPattern {
    /// Pattern type
    pub pattern_type: VersionPatternType,
    /// Version constraint
    pub constraint: String,
    /// Compiled regex if applicable
    pub regex: Option<regex::Regex>,
}

/// Version pattern types
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum VersionPatternType {
    /// Exact version match
    Exact,
    /// Semantic version range
    SemverRange,
    /// Regular expression
    Regex,
    /// Greater than
    GreaterThan,
    /// Less than
    LessThan,
    /// Between versions
    Between,
}

/// Component name variations for fuzzy matching
#[derive(Debug, Clone)]
pub struct ComponentNameVariations {
    /// Primary name
    pub primary: String,
    /// Alternative names
    pub alternatives: Vec<String>,
    /// Common abbreviations
    pub abbreviations: Vec<String>,
    /// Package manager specific names
    pub package_names: HashMap<String, String>,
}

impl CveMatcher {
    /// Create new CVE matcher
    pub fn new(config: &VulnerabilityConfig) -> Self {
        Self {
            config: config.clone(),
            version_patterns: HashMap::new(),
        }
    }

    /// Match vulnerabilities against components
    #[instrument(skip(self, components, vulnerabilities))]
    pub async fn match_vulnerabilities(
        &self,
        components: &[SoftwareComponent],
        vulnerabilities: &[Vulnerability],
    ) -> Result<Vec<CveMatch>> {
        info!(
            "Matching {} vulnerabilities against {} components",
            vulnerabilities.len(),
            components.len()
        );

        let mut matches = Vec::new();

        for component in components {
            for vulnerability in vulnerabilities {
                if let Some(cve_match) = self.match_component_vulnerability(component, vulnerability).await? {
                    matches.push(cve_match);
                }
            }
        }

        info!("Found {} CVE matches", matches.len());
        Ok(matches)
    }

    /// Match a single component against a vulnerability
    async fn match_component_vulnerability(
        &self,
        component: &SoftwareComponent,
        vulnerability: &Vulnerability,
    ) -> Result<Option<CveMatch>> {
        // Try different matching strategies in order of confidence
        
        // 1. Exact name and version match
        if let Some(match_result) = self.try_exact_match(component, vulnerability).await? {
            return Ok(Some(match_result));
        }

        // 2. Version range match
        if let Some(match_result) = self.try_version_range_match(component, vulnerability).await? {
            return Ok(Some(match_result));
        }

        // 3. CPE match
        if let Some(match_result) = self.try_cpe_match(component, vulnerability).await? {
            return Ok(Some(match_result));
        }

        // 4. PURL match
        if let Some(match_result) = self.try_purl_match(component, vulnerability).await? {
            return Ok(Some(match_result));
        }

        // 5. Fuzzy name match
        if let Some(match_result) = self.try_fuzzy_match(component, vulnerability).await? {
            return Ok(Some(match_result));
        }

        Ok(None)
    }

    /// Try exact name and version match
    async fn try_exact_match(
        &self,
        component: &SoftwareComponent,
        vulnerability: &Vulnerability,
    ) -> Result<Option<CveMatch>> {
        for affected_package in &vulnerability.affected {
            if self.normalize_name(&affected_package.package) == self.normalize_name(&component.name) {
                // Check if version matches exactly
                if affected_package.versions.contains(&component.version) {
                    return Ok(Some(CveMatch {
                        component: component.clone(),
                        vulnerability: vulnerability.clone(),
                        confidence: 1.0,
                        match_type: MatchType::ExactMatch,
                        match_details: MatchDetails {
                            matched_name: affected_package.package.clone(),
                            matched_version_range: Some(component.version.clone()),
                            cpe: None,
                            purl: None,
                            metadata: HashMap::new(),
                        },
                    }));
                }
            }
        }

        Ok(None)
    }

    /// Try version range match
    async fn try_version_range_match(
        &self,
        component: &SoftwareComponent,
        vulnerability: &Vulnerability,
    ) -> Result<Option<CveMatch>> {
        for affected_package in &vulnerability.affected {
            if self.normalize_name(&affected_package.package) == self.normalize_name(&component.name) {
                // Check version ranges
                for range in &affected_package.ranges {
                    if self.version_in_range(&component.version, range)? {
                        return Ok(Some(CveMatch {
                            component: component.clone(),
                            vulnerability: vulnerability.clone(),
                            confidence: 0.9,
                            match_type: MatchType::VersionRangeMatch,
                            match_details: MatchDetails {
                                matched_name: affected_package.package.clone(),
                                matched_version_range: Some(format!("{:?}", range)),
                                cpe: None,
                                purl: None,
                                metadata: HashMap::new(),
                            },
                        }));
                    }
                }
            }
        }

        Ok(None)
    }

    /// Try CPE (Common Platform Enumeration) match
    async fn try_cpe_match(
        &self,
        _component: &SoftwareComponent,
        _vulnerability: &Vulnerability,
    ) -> Result<Option<CveMatch>> {
        // TODO: Implement CPE matching
        Ok(None)
    }

    /// Try PURL (Package URL) match
    async fn try_purl_match(
        &self,
        _component: &SoftwareComponent,
        _vulnerability: &Vulnerability,
    ) -> Result<Option<CveMatch>> {
        // TODO: Implement PURL matching
        Ok(None)
    }

    /// Try fuzzy name matching
    async fn try_fuzzy_match(
        &self,
        component: &SoftwareComponent,
        vulnerability: &Vulnerability,
    ) -> Result<Option<CveMatch>> {
        for affected_package in &vulnerability.affected {
            let similarity = self.calculate_name_similarity(&component.name, &affected_package.package);
            
            if similarity > 0.8 {
                return Ok(Some(CveMatch {
                    component: component.clone(),
                    vulnerability: vulnerability.clone(),
                    confidence: similarity * 0.7, // Reduce confidence for fuzzy matches
                    match_type: MatchType::FuzzyMatch,
                    match_details: MatchDetails {
                        matched_name: affected_package.package.clone(),
                        matched_version_range: None,
                        cpe: None,
                        purl: None,
                        metadata: {
                            let mut meta = HashMap::new();
                            meta.insert("similarity_score".to_string(), similarity.to_string());
                            meta
                        },
                    },
                }));
            }
        }

        Ok(None)
    }

    /// Check if version is in the specified range
    fn version_in_range(
        &self,
        version: &str,
        range: &crate::vulnerability::VersionRange,
    ) -> Result<bool> {
        use crate::vulnerability::{EventType, RangeType};

        match range.range_type {
            RangeType::Semver => {
                self.check_semver_range(version, &range.events)
            }
            RangeType::Git => {
                // TODO: Implement Git commit range checking
                Ok(false)
            }
            RangeType::Ecosystem => {
                // TODO: Implement ecosystem-specific version checking
                Ok(false)
            }
        }
    }

    /// Check if version is in semantic version range
    fn check_semver_range(
        &self,
        version: &str,
        events: &[crate::vulnerability::RangeEvent],
    ) -> Result<bool> {
        use crate::vulnerability::EventType;

        let version = self.parse_version(version)?;
        let mut in_range = false;

        for event in events {
            let event_version = self.parse_version(&event.version)?;
            
            match event.event_type {
                EventType::Introduced => {
                    if version >= event_version {
                        in_range = true;
                    }
                }
                EventType::Fixed => {
                    if version >= event_version {
                        in_range = false;
                    }
                }
                EventType::LastAffected => {
                    if version > event_version {
                        in_range = false;
                    }
                }
                EventType::Limit => {
                    if version >= event_version {
                        in_range = false;
                    }
                }
            }
        }

        Ok(in_range)
    }

    /// Parse version string into comparable format
    fn parse_version(&self, version: &str) -> Result<semver::Version> {
        // Try to parse as semantic version
        if let Ok(semver) = semver::Version::parse(version) {
            return Ok(semver);
        }

        // Try to normalize and parse
        let normalized = self.normalize_version(version);
        semver::Version::parse(&normalized).map_err(|e| {
            crate::error::InfinitumError::Validation {
                field: "version".to_string(),
                message: format!("Invalid version format: {} ({})", version, e),
            }
        })
    }

    /// Normalize version string for parsing
    fn normalize_version(&self, version: &str) -> String {
        // Remove common prefixes
        let version = version.trim_start_matches('v');
        let version = version.trim_start_matches('V');
        
        // Handle versions like "1.2" -> "1.2.0"
        let parts: Vec<&str> = version.split('.').collect();
        match parts.len() {
            1 => format!("{}.0.0", parts[0]),
            2 => format!("{}.{}.0", parts[0], parts[1]),
            _ => version.to_string(),
        }
    }

    /// Normalize component name for comparison
    fn normalize_name(&self, name: &str) -> String {
        name.to_lowercase()
            .replace(['-', '_'], "")
            .replace(' ', "")
    }

    /// Calculate similarity between two names using Levenshtein distance
    fn calculate_name_similarity(&self, name1: &str, name2: &str) -> f64 {
        let name1 = self.normalize_name(name1);
        let name2 = self.normalize_name(name2);
        
        if name1 == name2 {
            return 1.0;
        }

        let distance = levenshtein_distance(&name1, &name2);
        let max_len = name1.len().max(name2.len());
        
        if max_len == 0 {
            return 1.0;
        }

        1.0 - (distance as f64 / max_len as f64)
    }

    /// Get component name variations for better matching
    fn get_name_variations(&self, component: &SoftwareComponent) -> ComponentNameVariations {
        let mut alternatives = Vec::new();
        let mut abbreviations = Vec::new();
        let mut package_names = HashMap::new();

        // Add common variations
        let name = &component.name;
        
        // Add with different separators
        alternatives.push(name.replace('-', "_"));
        alternatives.push(name.replace('_', "-"));
        alternatives.push(name.replace(['-', '_'], ""));

        // Add package manager specific names
        package_names.insert("npm".to_string(), name.clone());
        package_names.insert("cargo".to_string(), name.clone());
        package_names.insert("pypi".to_string(), name.clone());

        ComponentNameVariations {
            primary: name.clone(),
            alternatives,
            abbreviations,
            package_names,
        }
    }
}

/// Calculate Levenshtein distance between two strings
fn levenshtein_distance(s1: &str, s2: &str) -> usize {
    let len1 = s1.chars().count();
    let len2 = s2.chars().count();
    
    if len1 == 0 {
        return len2;
    }
    if len2 == 0 {
        return len1;
    }

    let mut matrix = vec![vec![0; len2 + 1]; len1 + 1];

    // Initialize first row and column
    for i in 0..=len1 {
        matrix[i][0] = i;
    }
    for j in 0..=len2 {
        matrix[0][j] = j;
    }

    let s1_chars: Vec<char> = s1.chars().collect();
    let s2_chars: Vec<char> = s2.chars().collect();

    for i in 1..=len1 {
        for j in 1..=len2 {
            let cost = if s1_chars[i - 1] == s2_chars[j - 1] { 0 } else { 1 };
            
            matrix[i][j] = (matrix[i - 1][j] + 1)
                .min(matrix[i][j - 1] + 1)
                .min(matrix[i - 1][j - 1] + cost);
        }
    }

    matrix[len1][len2]
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_levenshtein_distance() {
        assert_eq!(levenshtein_distance("", ""), 0);
        assert_eq!(levenshtein_distance("abc", "abc"), 0);
        assert_eq!(levenshtein_distance("abc", "ab"), 1);
        assert_eq!(levenshtein_distance("abc", "def"), 3);
    }

    #[test]
    fn test_name_normalization() {
        let config = VulnerabilityConfig::default();
        let matcher = CveMatcher::new(&config);
        
        assert_eq!(matcher.normalize_name("My-Package_Name"), "mypackagename");
        assert_eq!(matcher.normalize_name("UPPERCASE"), "uppercase");
    }

    #[test]
    fn test_version_normalization() {
        let config = VulnerabilityConfig::default();
        let matcher = CveMatcher::new(&config);
        
        assert_eq!(matcher.normalize_version("v1.2"), "1.2.0");
        assert_eq!(matcher.normalize_version("1.2"), "1.2.0");
        assert_eq!(matcher.normalize_version("1.2.3"), "1.2.3");
    }

    #[test]
    fn test_name_similarity() {
        let config = VulnerabilityConfig::default();
        let matcher = CveMatcher::new(&config);
        
        assert_eq!(matcher.calculate_name_similarity("test", "test"), 1.0);
        assert!(matcher.calculate_name_similarity("test", "tset") > 0.5);
        assert!(matcher.calculate_name_similarity("completely", "different") < 0.5);
    }
}
