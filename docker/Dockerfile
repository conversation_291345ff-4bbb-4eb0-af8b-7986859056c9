# Multi-stage Dockerfile for Infinitium Signal
# Production-ready container with security hardening

# Build stage
FROM rust:1.75-slim as builder

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    libpq-dev \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN useradd -m -u 1001 appuser

# Set working directory
WORKDIR /app

# Copy dependency files first for better caching
COPY Cargo.toml Cargo.lock ./
COPY src/lib.rs src/

# Build dependencies (this layer will be cached)
RUN cargo build --release --lib
RUN rm src/lib.rs

# Copy source code
COPY src/ src/
COPY build.rs ./

# Build the application
RUN cargo build --release

# Runtime stage
FROM debian:bookworm-slim

# Install runtime dependencies and security tools
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    libpq5 \
    curl \
    wget \
    jq \
    wkhtmltopdf \
    xvfb \
    && rm -rf /var/lib/apt/lists/*

# Install security scanning tools
RUN curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh -s -- -b /usr/local/bin \
    && curl -sSfL https://raw.githubusercontent.com/anchore/syft/main/install.sh | sh -s -- -b /usr/local/bin \
    && curl -sSfL https://raw.githubusercontent.com/anchore/grype/main/install.sh | sh -s -- -b /usr/local/bin

# Create app user and directories
RUN useradd -m -u 1001 appuser \
    && mkdir -p /app/data /app/logs /app/config /app/output \
    && chown -R appuser:appuser /app

# Copy binary from builder stage
COPY --from=builder /app/target/release/infinitum-signal /usr/local/bin/infinitum-signal
COPY --from=builder /app/target/release/infinitum-signal-cli /usr/local/bin/infinitum-signal-cli

# Copy configuration files
COPY docker/docker-entrypoint.sh /usr/local/bin/
COPY .env.example /app/config/
COPY monitoring/ /app/monitoring/
COPY logging/ /app/logging/

# Set permissions
RUN chmod +x /usr/local/bin/docker-entrypoint.sh \
    && chmod +x /usr/local/bin/infinitum-signal \
    && chmod +x /usr/local/bin/infinitum-signal-cli

# Security hardening
RUN apt-get update && apt-get install -y --no-install-recommends \
    dumb-init \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Switch to non-root user
USER appuser

# Set working directory
WORKDIR /app

# Environment variables
ENV RUST_LOG=info
ENV RUST_BACKTRACE=1
ENV APP_ENV=production
ENV DATABASE_URL=postgresql://infinitum_user:infinitum_pass@localhost:5432/infinitum_signal
ENV REDIS_URL=redis://localhost:6379
ENV API_HOST=0.0.0.0
ENV API_PORT=8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Expose ports
EXPOSE 8080 9090

# Use dumb-init for proper signal handling
ENTRYPOINT ["/usr/bin/dumb-init", "--"]

# Default command
CMD ["/usr/local/bin/docker-entrypoint.sh"]
