#!/bin/bash

# Infinitium Signal - Enterprise Cyber-Compliance Platform Setup Script
# This script sets up the complete development and production environment

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_warning "Running as root. This is recommended for system-wide installation."
    else
        log_info "Running as non-root user. Some operations may require sudo."
    fi
}

# Detect OS and package manager
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if command -v apt-get &> /dev/null; then
            PACKAGE_MANAGER="apt"
            INSTALL_CMD="apt-get update && apt-get install -y"
        elif command -v yum &> /dev/null; then
            PACKAGE_MANAGER="yum"
            INSTALL_CMD="yum install -y"
        elif command -v dnf &> /dev/null; then
            PACKAGE_MANAGER="dnf"
            INSTALL_CMD="dnf install -y"
        elif command -v pacman &> /dev/null; then
            PACKAGE_MANAGER="pacman"
            INSTALL_CMD="pacman -S --noconfirm"
        else
            log_error "Unsupported Linux distribution"
            exit 1
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        PACKAGE_MANAGER="brew"
        INSTALL_CMD="brew install"
    else
        log_error "Unsupported operating system: $OSTYPE"
        exit 1
    fi
    
    log_info "Detected OS: $OSTYPE with package manager: $PACKAGE_MANAGER"
}

# Install system dependencies
install_system_deps() {
    log_info "Installing system dependencies..."
    
    case $PACKAGE_MANAGER in
        "apt")
            sudo apt-get update
            sudo apt-get install -y \
                curl \
                wget \
                git \
                build-essential \
                pkg-config \
                libssl-dev \
                libpq-dev \
                postgresql-client \
                redis-tools \
                wkhtmltopdf \
                docker.io \
                docker-compose \
                jq \
                unzip \
                binwalk \
                file \
                strings \
                hexdump
            ;;
        "yum"|"dnf")
            sudo $PACKAGE_MANAGER install -y \
                curl \
                wget \
                git \
                gcc \
                gcc-c++ \
                make \
                openssl-devel \
                postgresql-devel \
                postgresql \
                redis \
                wkhtmltopdf \
                docker \
                docker-compose \
                jq \
                unzip \
                binwalk \
                file \
                util-linux
            ;;
        "pacman")
            sudo pacman -S --noconfirm \
                curl \
                wget \
                git \
                base-devel \
                openssl \
                postgresql-libs \
                postgresql \
                redis \
                wkhtmltopdf \
                docker \
                docker-compose \
                jq \
                unzip \
                binwalk \
                file \
                util-linux
            ;;
        "brew")
            brew install \
                curl \
                wget \
                git \
                openssl \
                postgresql \
                redis \
                wkhtmltopdf \
                docker \
                docker-compose \
                jq \
                unzip \
                binwalk \
                file
            ;;
    esac
    
    log_success "System dependencies installed"
}

# Install Rust and Cargo
install_rust() {
    if command -v rustc &> /dev/null; then
        log_info "Rust is already installed: $(rustc --version)"
        return
    fi
    
    log_info "Installing Rust..."
    curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
    source "$HOME/.cargo/env"
    
    # Install additional components
    rustup component add clippy rustfmt
    
    # Install useful cargo tools
    cargo install \
        cargo-watch \
        cargo-tarpaulin \
        diesel_cli \
        sqlx-cli \
        cargo-audit \
        cargo-outdated \
        cargo-tree
    
    log_success "Rust installed: $(rustc --version)"
}

# Install security scanning tools
install_security_tools() {
    log_info "Installing security scanning tools..."
    
    # Install Trivy
    if ! command -v trivy &> /dev/null; then
        log_info "Installing Trivy..."
        case $PACKAGE_MANAGER in
            "apt")
                wget -qO - https://aquasecurity.github.io/trivy-repo/deb/public.key | sudo apt-key add -
                echo "deb https://aquasecurity.github.io/trivy-repo/deb $(lsb_release -sc) main" | sudo tee -a /etc/apt/sources.list.d/trivy.list
                sudo apt-get update
                sudo apt-get install -y trivy
                ;;
            *)
                # Install via binary for other systems
                curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sudo sh -s -- -b /usr/local/bin
                ;;
        esac
    fi
    
    # Install Syft
    if ! command -v syft &> /dev/null; then
        log_info "Installing Syft..."
        curl -sSfL https://raw.githubusercontent.com/anchore/syft/main/install.sh | sudo sh -s -- -b /usr/local/bin
    fi
    
    # Install Grype
    if ! command -v grype &> /dev/null; then
        log_info "Installing Grype..."
        curl -sSfL https://raw.githubusercontent.com/anchore/grype/main/install.sh | sudo sh -s -- -b /usr/local/bin
    fi
    
    # Install ScanCode
    if ! command -v scancode &> /dev/null; then
        log_info "Installing ScanCode..."
        pip3 install scancode-toolkit
    fi
    
    log_success "Security scanning tools installed"
}

# Install Kubernetes tools
install_k8s_tools() {
    log_info "Installing Kubernetes tools..."
    
    # Install kubectl
    if ! command -v kubectl &> /dev/null; then
        log_info "Installing kubectl..."
        curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
        sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
        rm kubectl
    fi
    
    # Install Helm
    if ! command -v helm &> /dev/null; then
        log_info "Installing Helm..."
        curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
    fi
    
    # Install kind (for local testing)
    if ! command -v kind &> /dev/null; then
        log_info "Installing kind..."
        curl -Lo ./kind https://kind.sigs.k8s.io/dl/v0.20.0/kind-linux-amd64
        chmod +x ./kind
        sudo mv ./kind /usr/local/bin/kind
    fi
    
    log_success "Kubernetes tools installed"
}

# Install Terraform
install_terraform() {
    if command -v terraform &> /dev/null; then
        log_info "Terraform is already installed: $(terraform version)"
        return
    fi
    
    log_info "Installing Terraform..."
    wget -O- https://apt.releases.hashicorp.com/gpg | sudo gpg --dearmor -o /usr/share/keyrings/hashicorp-archive-keyring.gpg
    echo "deb [signed-by=/usr/share/keyrings/hashicorp-archive-keyring.gpg] https://apt.releases.hashicorp.com $(lsb_release -cs) main" | sudo tee /etc/apt/sources.list.d/hashicorp.list
    sudo apt update && sudo apt install terraform
    
    log_success "Terraform installed: $(terraform version)"
}

# Setup Docker
setup_docker() {
    log_info "Setting up Docker..."
    
    # Start Docker service
    sudo systemctl start docker
    sudo systemctl enable docker
    
    # Add current user to docker group
    sudo usermod -aG docker $USER
    
    log_success "Docker setup complete"
    log_warning "Please log out and log back in for Docker group changes to take effect"
}

# Setup PostgreSQL
setup_postgresql() {
    log_info "Setting up PostgreSQL..."
    
    # Start PostgreSQL service
    sudo systemctl start postgresql
    sudo systemctl enable postgresql
    
    # Create database and user
    sudo -u postgres createdb infinitum_signal || true
    sudo -u postgres psql -c "CREATE USER infinitum_user WITH PASSWORD 'infinitum_pass';" || true
    sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE infinitum_signal TO infinitum_user;" || true
    
    log_success "PostgreSQL setup complete"
}

# Setup Redis
setup_redis() {
    log_info "Setting up Redis..."
    
    # Start Redis service
    sudo systemctl start redis
    sudo systemctl enable redis
    
    log_success "Redis setup complete"
}

# Create project directories
create_directories() {
    log_info "Creating project directories..."
    
    mkdir -p {
        tests/{unit,integration,fixtures/{sample_repos,sample_firmware,expected_outputs}},
        demo-data/{sample_projects/{rust_web_app,node_express_api,python_ml_service,iot_device_code},sample_firmware,expected_reports},
        docs,
        monitoring/{prometheus,grafana/{dashboards,datasources}},
        logging/{loki,fluentd},
        scripts,
        docker,
        deployment/{kubernetes,helm/{templates},terraform}
    }
    
    log_success "Project directories created"
}

# Setup environment file
setup_environment() {
    log_info "Setting up environment configuration..."
    
    if [[ ! -f .env ]]; then
        cp .env.example .env
        log_info "Created .env file from .env.example"
        log_warning "Please edit .env file with your specific configuration"
    else
        log_info ".env file already exists"
    fi
}

# Build the project
build_project() {
    log_info "Building Infinitium Signal..."
    
    # Update dependencies
    cargo update
    
    # Build in release mode
    cargo build --release
    
    # Run tests
    cargo test
    
    log_success "Project built successfully"
}

# Setup monitoring stack
setup_monitoring() {
    log_info "Setting up monitoring stack..."
    
    # Create Prometheus configuration
    cat > monitoring/prometheus/prometheus.yml << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules.yml"

scrape_configs:
  - job_name: 'infinitum-signal'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 5s
    metrics_path: /metrics

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
EOF

    # Create Grafana datasource configuration
    cat > monitoring/grafana/datasources.yml << 'EOF'
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
EOF

    log_success "Monitoring stack configured"
}

# Generate final project structure
generate_project_structure() {
    log_info "Generating complete project structure..."

    # Create all necessary directories
    mkdir -p {
        src/{scanners,compliance,vulnerability,blockchain,database,api,orchestration,utils},
        tests/{unit,integration,fixtures/{sample_repos,sample_firmware,expected_outputs}},
        demo-data/{sample_projects/{rust_web_app,node_express_api,python_ml_service,iot_device_code},sample_firmware,expected_reports},
        docs,
        monitoring/{prometheus,grafana/{dashboards,datasources}},
        logging/{loki,fluentd},
        scripts,
        docker,
        deployment/{kubernetes,helm/{templates},terraform},
        .github/workflows
    }

    log_success "Project structure generated"
}

# Setup development environment
setup_dev_environment() {
    log_info "Setting up development environment..."

    # Install VS Code extensions (if VS Code is available)
    if command -v code &> /dev/null; then
        log_info "Installing VS Code extensions..."
        code --install-extension rust-lang.rust-analyzer
        code --install-extension vadimcn.vscode-lldb
        code --install-extension serayuzgur.crates
        code --install-extension tamasfe.even-better-toml
        code --install-extension ms-vscode.vscode-json
        code --install-extension redhat.vscode-yaml
        code --install-extension ms-kubernetes-tools.vscode-kubernetes-tools
        code --install-extension ms-vscode.vscode-docker
    fi

    # Setup Git hooks
    if [[ -d .git ]]; then
        log_info "Setting up Git hooks..."
        cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# Pre-commit hook for Infinitium Signal

set -e

echo "Running pre-commit checks..."

# Check formatting
cargo fmt --all -- --check

# Run Clippy
cargo clippy --all-targets --all-features -- -D warnings

# Run tests
cargo test --lib

echo "Pre-commit checks passed!"
EOF
        chmod +x .git/hooks/pre-commit
    fi

    log_success "Development environment setup complete"
}

# Validate installation
validate_installation() {
    log_info "Validating installation..."

    # Check if binaries were built successfully
    if [[ -f "target/release/infinitum-signal" ]]; then
        log_success "Main binary built successfully"
        ./target/release/infinitum-signal --version
    else
        log_error "Main binary not found"
        return 1
    fi

    if [[ -f "target/release/infinitum-signal-cli" ]]; then
        log_success "CLI binary built successfully"
        ./target/release/infinitum-signal-cli --help | head -5
    else
        log_error "CLI binary not found"
        return 1
    fi

    # Test database connection
    if systemctl is-active --quiet postgresql; then
        log_success "PostgreSQL is running"
    else
        log_warning "PostgreSQL is not running"
    fi

    # Test Redis connection
    if systemctl is-active --quiet redis; then
        log_success "Redis is running"
    else
        log_warning "Redis is not running"
    fi

    # Check security tools
    for tool in trivy syft grype; do
        if command -v $tool &> /dev/null; then
            log_success "$tool is installed: $(${tool} version 2>/dev/null | head -1 || echo 'version unknown')"
        else
            log_warning "$tool is not installed"
        fi
    done

    log_success "Installation validation complete"
}

# Generate demo data
generate_demo_data() {
    log_info "Generating demo data..."

    # Create sample Rust project with vulnerabilities
    mkdir -p demo-data/sample_projects/rust_web_app/src
    cat > demo-data/sample_projects/rust_web_app/Cargo.toml << 'EOF'
[package]
name = "sample-web-app"
version = "0.1.0"
edition = "2021"

[dependencies]
axum = "0.7"
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tracing = "0.1"
uuid = { version = "1.0", features = ["v4"] }
# Intentionally vulnerable dependencies for demo
openssl = "0.10.45"  # Known vulnerable version
time = "0.1.40"      # Known vulnerable version
EOF

    cat > demo-data/sample_projects/rust_web_app/src/main.rs << 'EOF'
use axum::{routing::get, Router};
use serde_json::json;

#[tokio::main]
async fn main() {
    let app = Router::new()
        .route("/", get(|| async { "Hello, Infinitium Signal!" }))
        .route("/health", get(|| async { json!({"status": "ok"}) }));

    println!("Starting sample web app on http://localhost:3000");
    axum::Server::bind(&"0.0.0.0:3000".parse().unwrap())
        .serve(app.into_make_service())
        .await
        .unwrap();
}
EOF

    # Create sample Node.js project
    mkdir -p demo-data/sample_projects/node_express_api
    cat > demo-data/sample_projects/node_express_api/package.json << 'EOF'
{
  "name": "sample-express-api",
  "version": "1.0.0",
  "description": "Sample Express.js API for demo",
  "main": "index.js",
  "dependencies": {
    "express": "4.18.2",
    "lodash": "4.17.20",
    "moment": "2.29.1",
    "axios": "0.21.1",
    "jsonwebtoken": "8.5.1"
  },
  "devDependencies": {
    "nodemon": "2.0.20"
  }
}
EOF

    # Create sample Python project
    mkdir -p demo-data/sample_projects/python_ml_service
    cat > demo-data/sample_projects/python_ml_service/requirements.txt << 'EOF'
flask==2.2.2
numpy==1.21.0
pandas==1.3.0
scikit-learn==1.0.2
requests==2.25.1
pillow==8.3.2
tensorflow==2.8.0
pyyaml==5.4.1
jinja2==2.11.3
EOF

    # Create sample firmware files
    mkdir -p demo-data/sample_firmware
    echo "Sample ESP32 firmware binary data" > demo-data/sample_firmware/esp32_firmware.bin
    echo "Arduino sketch binary" > demo-data/sample_firmware/arduino_sketch.elf
    echo "Linux embedded image" > demo-data/sample_firmware/linux_embedded.img

    log_success "Demo data generated"
}

# Main setup function
main() {
    log_info "Starting Infinitium Signal Enterprise Cyber-Compliance Platform Setup..."
    log_info "This will install and configure a complete production-ready cybersecurity platform"

    check_root
    detect_os
    install_system_deps
    install_rust
    install_security_tools
    install_k8s_tools
    install_terraform
    setup_docker
    setup_postgresql
    setup_redis
    generate_project_structure
    create_directories
    setup_environment
    setup_monitoring
    setup_dev_environment
    generate_demo_data
    build_project
    validate_installation

    log_header "🎉 INFINITIUM SIGNAL SETUP COMPLETED SUCCESSFULLY! 🎉"
    log_success "Enterprise Cyber-Compliance Platform is ready for use"
    log_info ""
    log_info "📋 NEXT STEPS:"
    log_info "1. Edit .env file with your configuration: nano .env"
    log_info "2. Setup database: make migrate"
    log_info "3. Start the platform: make run"
    log_info "4. Run comprehensive demo: ./scripts/demo.sh"
    log_info ""
    log_info "🌐 ACCESS POINTS:"
    log_info "• API Server: http://localhost:8080"
    log_info "• Health Check: http://localhost:8080/health"
    log_info "• API Documentation: http://localhost:8080/docs"
    log_info "• Metrics: http://localhost:9090/metrics"
    log_info ""
    log_info "🔧 USEFUL COMMANDS:"
    log_info "• CLI Help: ./target/release/infinitum-signal-cli --help"
    log_info "• Scan Project: ./target/release/infinitum-signal-cli scan sbom --target ./demo-data/sample_projects/rust_web_app"
    log_info "• Generate Report: ./target/release/infinitum-signal-cli compliance generate --framework cert-in"
    log_info "• Run Tests: make test"
    log_info "• View Logs: make logs"
    log_info ""
    log_info "📚 DOCUMENTATION:"
    log_info "• README.md - Project overview and quick start"
    log_info "• docs/ - Comprehensive documentation"
    log_info "• PROJECT_COMPLETION_SUMMARY.md - Complete feature list"
    log_info ""
    log_success "🚀 Ready for enterprise cybersecurity compliance automation!"
}

# Run main function
main "$@"
